"use client";

import { Arrow<PERSON><PERSON>tIcon, CalendarIcon, ClockIcon } from "@heroicons/react/24/outline";
import { motion, useScroll, useTransform } from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useRef } from "react";
import ReactMarkdown from "react-markdown";

import { BLOG_POSTS } from "@/constants/blog";
import { BlogPostType } from "@/types";

export const BlogDetailPage = ({ post }: { post: BlogPostType }) => {
  const containerRef = useRef<HTMLDivElement>(null);

  // Parallax effect for the header
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end start"]
  });

  const headerOpacity = useTransform(scrollYProgress, [0, 0.2], [1, 0]);
  const headerTranslateY = useTransform(scrollYProgress, [0, 0.2], [0, -100]);
  const imageScale = useTransform(scrollYProgress, [0, 0.2], [1, 1.1]);

  // Find related posts (same category, excluding current post)
  const relatedPosts = BLOG_POSTS
    .filter(p => p.category === post.category && p.id !== post.id)
    .slice(0, 3);

  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Animation variants for blog detail elements
  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3
      }
    }
  };

  const shimmer = {
    hidden: {
      backgroundPosition: "0% 0%"
    },
    visible: {
      backgroundPosition: "100% 0%",
      transition: {
        repeat: Infinity,
        repeatType: "mirror" as const,
        duration: 2,
        ease: "linear"
      }
    }
  };

  return (
    <div ref={containerRef} className="min-h-screen pt-16">
      {/* Hero section with parallax */}
      <motion.div
        className="relative h-[50vh] md:h-[60vh] w-full"
        style={{ opacity: headerOpacity }}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
      >
        <motion.div
          className="absolute inset-0 z-0"
          style={{ scale: imageScale }}
          initial={{ scale: 1.1 }}
          animate={{ scale: 1 }}
          transition={{ duration: 1.2, ease: "easeOut" }}
        >
          <Image
            src={post.image}
            alt={post.title}
            fill
            className="object-cover"
            priority
          />
          <motion.div
            className="absolute inset-0 bg-gradient-to-b from-[rgba(3,0,20,0.6)] to-[#030014]"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1, delay: 0.2 }}
          ></motion.div>
        </motion.div>

        <motion.div
          className="relative z-10 h-full flex flex-col justify-end px-4 md:px-8 pb-12 max-w-4xl mx-auto"
          style={{ y: headerTranslateY }}
          variants={staggerContainer}
          initial="hidden"
          animate="visible"
        >
          <motion.div
            className="mb-4"
            variants={fadeInUp}
          >
            <motion.span
              className="bg-purple-600 text-white text-sm px-3 py-1 rounded-full inline-block"
              whileHover={{
                scale: 1.05,
                boxShadow: "0 0 10px rgba(124, 58, 237, 0.5)"
              }}
              initial={{ x: -20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              {post.category}
            </motion.span>
          </motion.div>

          <motion.h1
            className="text-3xl md:text-5xl font-bold text-white mb-4"
            variants={fadeInUp}
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{
              duration: 0.7,
              delay: 0.3,
              type: "spring",
              stiffness: 50
            }}
          >
            <motion.span
              className="bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-cyan-400"
              variants={shimmer}
              initial="hidden"
              animate="visible"
              style={{
                backgroundSize: "200% 100%",
                display: "inline-block"
              }}
            >
              {post.title}
            </motion.span>
          </motion.h1>

          <motion.div
            className="flex flex-wrap items-center gap-4 text-gray-300 text-sm"
            variants={fadeInUp}
          >
            <motion.div
              className="flex items-center"
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
            >
              <motion.div
                className="relative w-8 h-8 rounded-full overflow-hidden mr-2"
                initial={{ scale: 0, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.6 }}
              >
                <Image
                  src={post.authorImage}
                  alt={post.author}
                  fill
                  className="object-cover"
                />
              </motion.div>
              <span>{post.author}</span>
            </motion.div>

            <motion.div
              className="flex items-center"
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
            >
              <CalendarIcon className="h-4 w-4 mr-1" />
              <span>{post.date}</span>
            </motion.div>

            <motion.div
              className="flex items-center"
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
            >
              <ClockIcon className="h-4 w-4 mr-1" />
              <span>{post.readTime}</span>
            </motion.div>
          </motion.div>
        </motion.div>
      </motion.div>

      {/* Content section */}
      <div className="bg-[#030014] py-12">
        <motion.div
          className="max-w-3xl mx-auto px-4 md:px-8 bg-[rgba(15,5,30,0.3)] rounded-xl border border-[#7042F88B]/30 p-6 md:p-10 shadow-lg shadow-purple-900/10"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{
            duration: 0.8,
            delay: 0.3,
            type: "spring",
            stiffness: 50
          }}
        >
          {/* Back button */}
          <motion.div
            initial={{ x: -20, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <Link
              href="/blog"
              className="inline-flex items-center text-gray-400 hover:text-white mb-8 transition-colors group"
            >
              <motion.span
                className="inline-flex items-center"
                whileHover={{ x: -3 }}
                transition={{ type: "spring", stiffness: 400 }}
              >
                <ArrowLeftIcon className="h-4 w-4 mr-2 group-hover:text-purple-400 transition-colors" />
                <span className="group-hover:text-purple-400 transition-colors">Back to Blog</span>
              </motion.span>
            </Link>
          </motion.div>

          {/* Article content */}
          <motion.article
            className="prose prose-lg max-w-none prose-headings:text-purple-50 prose-headings:font-bold prose-p:text-gray-200 prose-a:text-purple-400 prose-a:no-underline hover:prose-a:text-purple-300 hover:prose-a:underline prose-strong:text-white prose-blockquote:text-gray-300 prose-blockquote:border-purple-600 prose-blockquote:bg-purple-900/20 prose-blockquote:rounded-md prose-blockquote:py-1 prose-blockquote:px-4 prose-ul:text-gray-200 prose-ol:text-gray-200 prose-li:marker:text-purple-400 prose-pre:bg-[rgba(15,5,30,0.8)] prose-pre:border prose-pre:border-purple-600/30 prose-pre:rounded-lg prose-code:text-purple-200 prose-code:bg-purple-900/30 prose-code:px-1 prose-code:py-0.5 prose-code:rounded prose-code:before:content-none prose-code:after:content-none"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1, delay: 0.6 }}
          >
            <ReactMarkdown>{post.content}</ReactMarkdown>
          </motion.article>

          {/* Author info */}
          <motion.div
            className="mt-12 p-6 bg-gradient-to-br from-purple-900/30 to-cyan-900/20 border border-purple-500/20 rounded-xl shadow-lg shadow-purple-900/10"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            whileHover={{
              boxShadow: "0 20px 25px -5px rgba(124, 58, 237, 0.2), 0 10px 10px -5px rgba(124, 58, 237, 0.1)",
              borderColor: "rgba(139, 92, 246, 0.4)"
            }}
          >
            <div className="flex flex-col sm:flex-row items-center sm:items-start text-center sm:text-left">
              <motion.div
                className="relative w-20 h-20 rounded-full overflow-hidden mb-4 sm:mb-0 sm:mr-6 border-2 border-purple-500/30 shadow-lg shadow-purple-900/20"
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.5, delay: 1 }}
                whileHover={{ scale: 1.05 }}
              >
                <Image
                  src={post.authorImage}
                  alt={post.author}
                  fill
                  className="object-cover"
                />
              </motion.div>
              <motion.div
                variants={staggerContainer}
                initial="hidden"
                animate="visible"
              >
                <motion.h3
                  className="text-xl font-bold text-white mb-1"
                  variants={fadeInUp}
                >
                  {post.author}
                </motion.h3>
                <motion.p
                  className="text-purple-300 mb-3"
                  variants={fadeInUp}
                >
                  Content Writer at Oyu Intelligence
                </motion.p>
                <motion.p
                  className="text-gray-300 text-sm"
                  variants={fadeInUp}
                >
                  Expert in {post.category} with a passion for creating engaging, informative content that helps businesses succeed in the digital landscape.
                </motion.p>
              </motion.div>
            </div>
          </motion.div>

          {/* Share buttons */}
          <motion.div
            className="mt-10 flex flex-col sm:flex-row items-center sm:items-start sm:justify-between gap-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 1 }}
          >
            <div className="text-gray-300 font-medium">Share this article:</div>
            <div className="flex space-x-3">
              {['twitter', 'facebook', 'linkedin'].map((platform, index) => (
                <motion.button
                  key={platform}
                  className="w-12 h-12 rounded-full bg-gradient-to-br from-purple-900/40 to-cyan-900/30 border border-purple-500/30 flex items-center justify-center hover:from-purple-800/50 hover:to-cyan-800/40 transition-all duration-300 shadow-md hover:shadow-purple-900/30"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{
                    duration: 0.4,
                    delay: 1.1 + (index * 0.1),
                    type: "spring",
                    stiffness: 200
                  }}
                  whileHover={{
                    scale: 1.1,
                    rotate: [0, -5, 5, -5, 0],
                    transition: { duration: 0.5 }
                  }}
                  aria-label={`Share on ${platform}`}
                >
                  <Image
                    src={`/icons/${platform}.svg`}
                    alt={platform}
                    width={24}
                    height={24}
                  />
                </motion.button>
              ))}
            </div>
          </motion.div>
        </motion.div>
      </div>

      {/* Related posts section */}
      {relatedPosts.length > 0 && (
        <div className="bg-gradient-to-b from-[#030014] to-[rgba(30,10,60,0.2)] py-20">
          <motion.div
            className="max-w-6xl mx-auto px-4 md:px-8"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <motion.div
              className="text-center mb-12"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{
                duration: 0.7,
                delay: 0.4,
                type: "spring",
                stiffness: 50
              }}
            >
              <motion.h2
                className="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-cyan-400 mb-4"
                variants={shimmer}
                initial="hidden"
                animate="visible"
                style={{
                  backgroundSize: "200% 100%",
                  display: "inline-block"
                }}
              >
                Related Articles
              </motion.h2>
              <motion.p
                className="text-gray-300 max-w-2xl mx-auto"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.6 }}
              >
                Explore more content related to {post.category} that might interest you
              </motion.p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {relatedPosts.map((relatedPost, index) => (
                <motion.div
                  key={relatedPost.id}
                  initial={{ opacity: 0, y: 50 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{
                    duration: 0.6,
                    delay: 0.7 + (index * 0.2),
                    type: "spring",
                    stiffness: 50
                  }}
                  whileHover={{ y: -10 }}
                >
                  <Link href={`/blog/${relatedPost.slug}`} className="group block">
                    <motion.div
                      className="bg-[rgba(15,5,30,0.4)] rounded-xl border border-purple-500/20 overflow-hidden h-full flex flex-col shadow-lg shadow-purple-900/10"
                      whileHover={{
                        borderColor: "rgba(139, 92, 246, 0.5)",
                        boxShadow: "0 20px 25px -5px rgba(124, 58, 237, 0.1), 0 10px 10px -5px rgba(124, 58, 237, 0.04)"
                      }}
                    >
                      <div className="relative h-48 w-full overflow-hidden">
                        <motion.div whileHover={{ scale: 1.1 }} transition={{ duration: 0.4 }}>
                          <Image
                            src={relatedPost.image}
                            alt={relatedPost.title}
                            fill
                            className="object-cover"
                          />
                        </motion.div>
                        <div className="absolute inset-0 bg-gradient-to-t from-[rgba(15,5,30,0.8)] to-transparent opacity-60 group-hover:opacity-40 transition-opacity duration-300"></div>
                        <motion.div
                          className="absolute top-4 right-4 bg-purple-600 text-white text-xs px-2 py-1 rounded-full"
                          initial={{ x: 20, opacity: 0 }}
                          animate={{ x: 0, opacity: 1 }}
                          transition={{ duration: 0.5, delay: 0.9 + (index * 0.2) }}
                          whileHover={{ scale: 1.05 }}
                        >
                          {relatedPost.category}
                        </motion.div>
                      </div>
                      <motion.div
                        className="p-6 flex-grow"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.5, delay: 1 + (index * 0.2) }}
                      >
                        <h3 className="text-xl font-bold text-white mb-3 group-hover:text-purple-300 transition-colors">
                          {relatedPost.title}
                        </h3>
                        <p className="text-gray-300 text-sm mb-4 line-clamp-2">
                          {relatedPost.excerpt}
                        </p>
                        <div className="flex items-center text-sm text-gray-400">
                          <span>{relatedPost.date}</span>
                          <span className="mx-2">•</span>
                          <span>{relatedPost.readTime}</span>
                        </div>
                      </motion.div>
                    </motion.div>
                  </Link>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      )}

      {/* CTA section */}
      <div className="bg-gradient-to-br from-purple-900/30 to-cyan-900/20 py-20">
        <div className="max-w-4xl mx-auto px-4 md:px-8 text-center relative">
          {/* Decorative elements */}
          <div className="absolute top-0 left-0 w-32 h-32 bg-purple-600/10 rounded-full blur-3xl -z-10"></div>
          <div className="absolute bottom-0 right-0 w-32 h-32 bg-cyan-600/10 rounded-full blur-3xl -z-10"></div>

          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">Ready to start your project?</h2>
          <p className="text-gray-200 mb-10 max-w-2xl mx-auto text-lg">
            Let&apos;s discuss how Oyu Intelligence can help bring your vision to life with our expertise in {post.category}.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/#contact"
              className="px-8 py-4 bg-gradient-to-r from-purple-600 to-cyan-600 text-white rounded-lg hover:opacity-90 transition-all duration-300 inline-block font-medium shadow-lg shadow-purple-900/30 hover:shadow-purple-800/40 hover:-translate-y-1"
            >
              Contact Us
            </Link>
            <Link
              href="/#services"
              className="px-8 py-4 bg-[rgba(30,10,60,0.4)] border border-purple-500/30 text-white rounded-lg hover:bg-[rgba(30,10,60,0.6)] transition-all duration-300 inline-block font-medium hover:-translate-y-1"
            >
              Explore Services
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};
