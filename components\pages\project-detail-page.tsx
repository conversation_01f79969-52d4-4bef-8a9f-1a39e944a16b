"use client";

import { ArrowLeftIcon } from "@heroicons/react/24/outline";
import { motion } from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import { useEffect } from "react";

import { ProjectType } from "@/types";

export const ProjectDetailPage = ({ project }: { project: ProjectType }) => {
  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <div className="flex flex-col min-h-screen py-20 px-4 mt-16 max-w-7xl mx-auto">
      {/* Back Button */}
      <motion.div
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.5 }}
        className="mb-8"
      >
        <Link
          href="/projects"
          className="flex items-center text-gray-400 hover:text-white transition-colors"
        >
          <ArrowLeftIcon className="h-5 w-5 mr-2" />
          Back to Projects
        </Link>
      </motion.div>

      {/* Project Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="mb-12"
      >
        <h1 className="text-4xl font-bold text-white mb-4">{project.title}</h1>
        <div className="flex flex-wrap gap-3 mb-6">
          {project.technologies.map((tech) => (
            <span
              key={tech}
              className="px-3 py-1 bg-[rgba(3,0,20,0.5)] border border-[#7042F88B] rounded-full text-sm text-gray-300"
            >
              {tech}
            </span>
          ))}
        </div>
        <p className="text-gray-400 text-lg">{project.shortDescription || project.description.substring(0, 150)}</p>
      </motion.div>

      {/* Project Image */}
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.7 }}
        className="w-full h-[400px] md:h-[500px] relative rounded-xl overflow-hidden mb-12"
      >
        <Image
          src={project.image}
          alt={project.title}
          fill
          className="object-cover"
        />
      </motion.div>

      {/* Project Details */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="md:col-span-2"
        >
          <h2 className="text-2xl font-bold text-white mb-6">Project Overview</h2>
          <div className="text-gray-300 space-y-4">
            {project.description.split('\n\n').map((paragraph, index) => (
              <p key={index}>{paragraph}</p>
            ))}
          </div>

          {project.challenges && (
            <div className="mt-8">
              <h3 className="text-xl font-bold text-white mb-4">Challenges</h3>
              <ul className="list-disc list-inside text-gray-300 space-y-2">
                {project.challenges.map((challenge, index) => (
                  <li key={index}>{challenge}</li>
                ))}
              </ul>
            </div>
          )}

          {project.solutions && (
            <div className="mt-8">
              <h3 className="text-xl font-bold text-white mb-4">Solutions</h3>
              <ul className="list-disc list-inside text-gray-300 space-y-2">
                {project.solutions.map((solution, index) => (
                  <li key={index}>{solution}</li>
                ))}
              </ul>
            </div>
          )}
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="bg-[rgba(3,0,20,0.5)] border border-[#7042F88B] rounded-xl p-6"
        >
          <h2 className="text-xl font-bold text-white mb-6">Project Details</h2>
          
          <div className="space-y-4">
            <div>
              <h3 className="text-gray-400 text-sm">CLIENT</h3>
              <p className="text-white">{project.client}</p>
            </div>
            
            <div>
              <h3 className="text-gray-400 text-sm">CATEGORY</h3>
              <p className="text-white">{project.category}</p>
            </div>
            
            <div>
              <h3 className="text-gray-400 text-sm">COMPLETION</h3>
              <p className="text-white">{project.completionDate}</p>
            </div>
            
            <div>
              <h3 className="text-gray-400 text-sm">PROJECT URL</h3>
              <a 
                href={project.link} 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-purple-400 hover:text-purple-300 transition-colors"
              >
                Visit Project
              </a>
            </div>
          </div>

          {project.results && (
            <div className="mt-8">
              <h3 className="text-xl font-bold text-white mb-4">Results</h3>
              <ul className="space-y-3">
                {project.results.map((result, index) => (
                  <li key={index} className="flex items-start">
                    <span className="text-green-400 mr-2">✓</span>
                    <span className="text-gray-300">{result}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </motion.div>
      </div>

      {/* Related Projects */}
      {project.relatedProjects && project.relatedProjects.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
          className="mb-16"
        >
          <h2 className="text-2xl font-bold text-white mb-8">Related Projects</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {project.relatedProjects.map((relatedProject, index) => (
              <Link key={index} href={`/projects/${relatedProject.slug}`}>
                <div className="bg-[rgba(3,0,20,0.5)] border border-[#7042F88B] rounded-xl overflow-hidden hover:border-purple-500 transition-colors">
                  <div className="relative h-48 w-full">
                    <Image
                      src={relatedProject.image}
                      alt={relatedProject.title}
                      fill
                      className="object-cover"
                    />
                  </div>
                  <div className="p-4">
                    <h3 className="text-white font-bold">{relatedProject.title}</h3>
                    <p className="text-gray-400 text-sm mt-2">{relatedProject.category}</p>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </motion.div>
      )}

      {/* CTA */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.8 }}
        className="text-center bg-gradient-to-r from-purple-900/20 to-cyan-900/20 border border-[#7042F88B] rounded-xl p-8 mb-8"
      >
        <h2 className="text-2xl font-bold text-white mb-4">Ready to start your project?</h2>
        <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
          Lets discuss how Oyu Intelligence can help bring your vision to life with our expertise in {project.category}.
        </p>
        <Link
          href="/#contact"
          className="px-8 py-3 button-primary text-white rounded-lg hover:opacity-90 transition-opacity inline-block"
        >
          Contact Us
        </Link>
      </motion.div>
    </div>
  );
};
