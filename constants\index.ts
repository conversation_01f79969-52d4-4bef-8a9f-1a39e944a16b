import { FaFacebook } from "react-icons/fa";
import {
  RxInstagram<PERSON>ogo,
  RxLinkedinLogo,
} from "react-icons/rx";

export { ALL_PROJECTS } from "./projects";

export const SKILL_DATA = [
  {
    skill_name: "HTML",
    image: "ai-int.avif",
    width: 80,
    height: 80,
  },
  {
    skill_name: "CSS",
    image: "Kotlin_Icon.png",
    width: 65,
    height: 65,
  },
  {
    skill_name: "JavaScript",
    image: "js.png",
    width: 65,
    height: 65,
  },
  {
    skill_name: "Tailwind CSS",
    image: "tailwind.png",
    width: 80,
    height: 80,
  },
  {
    skill_name: "React",
    image: "react.png",
    width: 80,
    height: 80,
  },
  {
    skill_name: "Redux",
    image: "skit.png",
    width: 115,
    height: 115,
  },
  {
    skill_name: "React Query",
    image: "firebase.png",
    width: 60,
    height: 65,
  },
  {
    skill_name: "TypeScript",
    image: "ts.png",
    width: 80,
    height: 80,
  },
  {
    skill_name: "Next.js 14",
    image: "next.png",
    width: 80,
    height: 80,
  },
  {
    skill_name: "Framer Motion",
    image: "framer.png",
    width: 80,
    height: 80,
  },
  {
    skill_name: "Stripe",
    image: "pytorch.png",
    width: 80,
    height: 80,
  },
  {
    skill_name: "Node.js",
    image: "node.png",
    width: 80,
    height: 80,
  },
  {
    skill_name: "MongoDB",
    image: "mongodb.png",
    width: 40,
    height: 40,
  },
] as const;

export const SOCIALS = [
  {
    name: "Instagram",
    icon: RxInstagramLogo,
    link: "https://www.instagram.com/oyu_intelligence/",
  },
  {
    name: "Facebook",
    icon: FaFacebook,
    link: "https://www.facebook.com/Oyu.Intelligence",
  },
  {
    name: "LinkedIn",
    icon: RxLinkedinLogo,
    link: "http://linkedin.com/company/oyu-intelligence",
  },
] as const;

export const FRONTEND_SKILL = [
  {
    skill_name: "HubX",
    image: "unnamed.jpg",
    width: 80,
    height: 80,
  },
  {
    skill_name: "Apex",
    image: "Logo.jpg",
    width: 80,
    height: 80,
  },
  {
    skill_name: "Debate",
    image: "debate.png",
    width: 100,
    height: 100,
  },
  {
    skill_name: "Tailwind CSS",
    image: "prime.jpg",
    width: 100,
    height: 100,
  },
  {
    skill_name: "innohb",
    image: "innohub.jpg",
    width: 80,
    height: 80,
  },
  {
    skill_name: "LookLuxe",
    image: "lookluxe.png",
    width: 120,
    height: 120,
  },
  {
    skill_name: "Ehlel",
    image: "Ehlel.jpg",
    width: 80,
    height: 80,
  },
  {
    skill_name: "OUG",
    image: "oug.png",
    width: 80,
    height: 80,
  },
  {
    skill_name: "must",
    image: "must.png",
    width: 85,
    height: 85,
  },
  {
    skill_name: "Ufe",
    image: "ufe.png",
    width: 60,
    height: 60,
  },
] as const;

export const BACKEND_SKILL = [
  {
    skill_name: "Node.js",
    image: "anizet.png",
    width: 110,
    height: 110,
  },
  {
    skill_name: "Express.js",
    image: "moffice.jpg",
    width: 80,
    height: 80,
  },
  {
    skill_name: "MongoDB",
    image: "aiby.png",
    width: 150,
    height: 150,
  },
  {
    skill_name: "golomt",
    image: "golomt.png",
    width: 80,
    height: 80,
  },
  {
    skill_name: "Oyu AI",
    image: "oyu-ai.jpg",
    width: 80,
    height: 80,
  },
  {
    skill_name: "Mobicom",
    image: "Mobicom.png",
    width: 120,
    height: 120,
  },
  {
    skill_name: "Prisma",
    image: "tav.webp",
    width: 150,
    height: 150,
  },
  {
    skill_name: "Graphql",
    image: "gfs.png",
    width: 80,
    height: 80,
  },
] as const;

export const FULLSTACK_SKILL = [
  {
    skill_name: "neo",
    image: "icon1.png",
    width: 100,
    height: 100,
  },
  {
    skill_name: "OZ",
    image: "oz-logo.png",
    width: 120,
    height: 120,
  },
  {
    skill_name: "OM",
    image: "neo.png",
    width: 80,
    height: 80,
  },

  {
    skill_name: "Astro",
    image: "astro-white.png",
    width: 100,
    height: 100,
  },
] as const;

export const OTHER_SKILL = [
  {
    skill_name: "Oyu",
    image: "oyu-int.png",
    width: 150,
    height: 150,
  },
] as const;

// Featured projects for the homepage
export const PROJECTS = [
  {
    title: "LookLuxe AI Outfit Assistant",
    description:
      "A premium fashion application that leverages AI to deliver personalized style recommendations, outfit planning, and wardrobe management. The app combines advanced analytics and eco-conscious fashion insights to help users refine their personal style while making sustainable choices.",
    image: "/projects/lookluxe.jpg",
    link: "/projects/lookluxe-ai-outfit-assistant",
  },
  {
    title: "Astro AI - Chatbot Assistant",
    description:
      "An all-in-one next-generation AI assistant app that brings together multiple powerful LLMs and a network of smart AI agents under a single subscription model. Designed for both utility and entertainment with Agent-to-Agent communication and persona-driven chatbots.",
    image: "/projects/astro-ai.jpg",
    link: "/projects/astro-ai-chatbot-assistant",
  },
  {
    title: "Neo AI - Logo Generator",
    description:
      "An innovative mobile application that leverages artificial intelligence to generate custom logos and brand assets for businesses and individuals. The app provides an intuitive interface for users to create unique logo designs that can be customized and exported in various formats.",
    image: "/projects/neo-ai.jpg",
    link: "/projects/neo-ai-logo-generator",
  },
] as const;

export const FOOTER_DATA = [
  {
    title: "Services",
    data: [
      {
        name: "AI Automation",
        icon: null,
        link: "#services",
      },
      {
        name: "Mobile App Development",
        icon: null,
        link: "#services-detail",
      },
      {
        name: "Website Development",
        icon: null,
        link: "#services-detail",
      },
      {
        name: "Social Media Development",
        icon: null,
        link: "#services-detail",
      },
    ],
  },
  {
    title: "Company",
    data: [
      {
        name: "About Us",
        icon: null,
        link: "#about-us",
      },
      {
        name: "Company Info",
        icon: null,
        link: "/company",
      },
      {
        name: "Blog",
        icon: null,
        link: "/blog",
      },
      {
        name: "Portfolio",
        icon: null,
        link: "#portfolio",
      },
    ],
  },
  {
    title: "Social Media",
    data: [
      {
        name: "Instagram",
        icon: RxInstagramLogo,
        link: "https://www.instagram.com/oyu_intelligence/",
      },
      {
        name: "Facebook",
        icon: FaFacebook,
        link: "https://www.facebook.com/Oyu.Intelligence",
      },
      {
        name: "LinkedIn",
        icon: RxLinkedinLogo,
        link: "http://linkedin.com/company/oyu-intelligence",
      },
    ],
  },
  {
    title: "Contact",
    data: [
      {
        name: "Email Us",
        icon: null,
        link: "mailto:<EMAIL>",
      },
      {
        name: "Call Us",
        icon: null,
        link: "tel:86970213",
      },
      {
        name: "Contact Form",
        icon: null,
        link: "#contact",
      },
    ],
  },
] as const;

export const NAV_LINKS = [
  {
    title: "About Us",
    link: "#about-us",
  },
  {
    title: "Services",
    link: "#services",
  },
  {
    title: "Portfolio",
    link: "#portfolio",
  },
  {
    title: "Blog",
    link: "/blog",
  },
  {
    title: "Company",
    link: "/company",
  },
  {
    title: "Contact",
    link: "#contact",
  },
] as const;

export const LINKS = {
  sourceCode: "https://github.com/boldbat/Oyu-Mind-Space",
};
