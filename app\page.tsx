"use client";

import { motion } from "framer-motion";

import { About } from "@/components/main/about";
import { BlogPreview } from "@/components/main/blog-preview";
import { Contact } from "@/components/main/contact";
import { Encryption } from "@/components/main/encryption";
import { GoogleGeminiSection } from "@/components/main/google-gemini-section";
import { Hero } from "@/components/main/hero";
import { Projects } from "@/components/main/projects";
import { Services } from "@/components/main/services";
import { Skills } from "@/components/main/skills";
import { Testimonials } from "@/components/main/testimonials";
import { pageVariants } from "@/lib/motion";

export default function Home() {
  return (
    <motion.main
      className="h-full w-full"
      initial="initial"
      animate="animate"
      exit="exit"
      variants={pageVariants}
    >
      <div className="flex flex-col gap-20">
        <Hero />
        <About />
        <Skills />
        <Encryption />
        <Services />
        <GoogleGeminiSection />
        <Projects />
        <Testimonials />
        <BlogPreview />
        <Contact />
      </div>
    </motion.main>
  );
}
