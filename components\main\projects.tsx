'use client';

import { ProjectCard } from "@/components/sub/project-card";
import { PROJECTS } from "@/constants";
import { motion } from "framer-motion";
import Link from "next/link";

export const Projects = () => {
  return (
    <section
      id="portfolio"
      className="flex flex-col items-center justify-center py-20 relative overflow-hidden"
    >
      {/* Background elements */}
      <div className="absolute top-1/4 left-0 w-72 h-72 bg-purple-900/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-1/4 right-0 w-72 h-72 bg-cyan-900/10 rounded-full blur-3xl"></div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        viewport={{ once: true }}
        className="relative z-10"
      >
        <div className="text-center mb-8">
          <div className="Welcome-box py-[8px] px-[7px] border border-[#7042f88b] opacity-[0.9]] mb-6 mx-auto w-fit">
            <h1 className="Welcome-text text-[13px]">
              Our Work
            </h1>
          </div>

          <h1 className="text-4xl md:text-[40px] font-semibold text-transparent bg-clip-text bg-gradient-to-r from-purple-500 to-cyan-500 mb-4">
            Our Portfolio
          </h1>

          <p className="text-gray-400 max-w-2xl mx-auto px-4 mb-12">
            Explore our latest projects showcasing innovative AI solutions, mobile applications, and digital experiences that deliver exceptional results for our clients.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 px-4 sm:px-6 md:px-10 max-w-7xl mx-auto">
          {PROJECTS.map((project, index) => (
            <motion.div
              key={project.title}
              initial={{ opacity: 0, y: 50, scale: 0.95 }}
              whileInView={{ opacity: 1, y: 0, scale: 1 }}
              transition={{
                duration: 0.7,
                delay: index * 0.1,
                ease: [0.25, 0.1, 0.25, 1]
              }}
              viewport={{ once: true, margin: "-50px" }}
              whileHover={{
                y: -10,
                transition: { duration: 0.3, ease: "easeOut" }
              }}
              className="will-change-transform"
            >
              <Link href={project.link} className="block h-full">
                <ProjectCard
                  src={project.image}
                  title={project.title}
                  description={project.description}
                  link={project.link}
                  category="AI Automation"
                  isLink={false}
                />
              </Link>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          viewport={{ once: true }}
          className="text-center mt-12"
        >
          <Link
            href="/projects"
            className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-600/80 to-cyan-600/80 text-white rounded-full text-sm hover:from-purple-600 hover:to-cyan-600 transition-all duration-300 group shadow-lg shadow-purple-900/20"
          >
            <span>View All Projects</span>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
            </svg>
          </Link>
        </motion.div>
      </motion.div>
    </section>
  );
};
