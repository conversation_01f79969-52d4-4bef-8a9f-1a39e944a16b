"use client";

import { StarIcon } from "@heroicons/react/24/solid";
import { AnimatePresence, motion, useInView } from "framer-motion";
import Image from "next/image";
import { useCallback, useEffect, useRef, useState } from "react";

import { slideInFromLeft, slideInFromTop } from "@/lib/motion";
import { TestimonialType } from "@/types";

// Add type for animation options
type KeyframeAnimationOptions = {
  duration: number;
  easing: string;
  fill: 'forwards' | 'backwards' | 'both' | 'none';
};

// Testimonial card component
const TestimonialCard = ({
  testimonial,
  index,
  isActive
}: {
  testimonial: TestimonialType;
  index: number;
  isActive: boolean;
}) => {
  const cardRef = useRef(null);
  const isInView = useInView(cardRef, { once: true });

  return (
    <motion.div
      ref={cardRef}
      initial={{ opacity: 0, y: 50 }}
      animate={isInView ? {
        opacity: isActive ? 1 : 0.5,
        y: 0,
        scale: isActive ? 1 : 0.95
      } : { opacity: 0, y: 50 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      className={`bg-gradient-to-b from-[rgba(30,10,60,0.5)] to-[rgba(3,0,20,0.7)] rounded-xl border ${
        isActive ? "border-purple-500" : "border-[#7042F88B]"
      } p-6 shadow-lg shadow-purple-900/10 transition-all duration-300 h-full flex flex-col`}
    >
      {/* Quote icon */}
      <div className="text-purple-500 mb-4">
        <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M10.667 13.333H5.33366C4.96547 13.333 4.66699 13.0345 4.66699 12.6663V7.33301C4.66699 6.96482 4.96547 6.66634 5.33366 6.66634H10.667C11.0352 6.66634 11.3337 6.96482 11.3337 7.33301V12.6663C11.3337 13.0345 11.0352 13.333 10.667 13.333Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M26.6663 13.333H21.333C20.9648 13.333 20.6663 13.0345 20.6663 12.6663V7.33301C20.6663 6.96482 20.9648 6.66634 21.333 6.66634H26.6663C27.0345 6.66634 27.333 6.96482 27.333 7.33301V12.6663C27.333 13.0345 27.0345 13.333 26.6663 13.333Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M10.667 29.3337H5.33366C4.96547 29.3337 4.66699 29.0352 4.66699 28.667V23.3337C4.66699 22.9655 4.96547 22.667 5.33366 22.667H10.667C11.0352 22.667 11.3337 22.9655 11.3337 23.3337V28.667C11.3337 29.0352 11.0352 29.3337 10.667 29.3337Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M26.6663 29.3337H21.333C20.9648 29.3337 20.6663 29.0352 20.6663 28.667V23.3337C20.6663 22.9655 20.9648 22.667 21.333 22.667H26.6663C27.0345 22.667 27.333 22.9655 27.333 23.3337V28.667C27.333 29.0352 27.0345 29.3337 26.6663 29.3337Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M11.333 10H20.6663" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M11.333 26H20.6663" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M8 13.333V22.6663" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M24 13.333V22.6663" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      </div>

      {/* Testimonial text */}
      <p className="text-gray-300 italic mb-6 flex-grow">{testimonial.testimonial}</p>

      {/* Rating */}
      <div className="flex mb-4">
        {[1, 2, 3, 4, 5].map((starNumber) => (
          <StarIcon
            key={`star-${testimonial.id}-${starNumber}`}
            className={`h-5 w-5 ${starNumber <= testimonial.rating ? "text-yellow-500" : "text-gray-600"}`}
          />
        ))}
      </div>

      {/* Client info */}
      <div className="flex items-center">
        <div className="relative w-12 h-12 rounded-full overflow-hidden mr-4 border border-purple-500/50">
          <Image
            src={testimonial.image}
            alt={testimonial.name}
            fill
            className="object-cover"
          />
        </div>
        <div>
          <h4 className="text-white font-medium">{testimonial.name}</h4>
          <p className="text-gray-400 text-sm">{testimonial.position}, {testimonial.company}</p>
        </div>
      </div>
    </motion.div>
  );
};

export const Testimonials = () => {
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, { once: true, amount: 0.2 });
  const [activeIndex, setActiveIndex] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const progressBarRef = useRef<HTMLDivElement>(null);
  const progressAnimationRef = useRef<Animation | null>(null);

  const testimonials: TestimonialType[] = [
    {
      id: 1,
      name: "王OK",
      position: "Singer",
      company: "88rising",
      testimonial: "The team at Oyu Mind was incredibly professional and a pleasure to work with. They took the time to understand our business needs and delivered a website that exceeded our expectations. Our online presence has grown significantly since the launch, and we&apos;ve received numerous compliments on the design and functionality. Highly recommend!",
      image: "/testimonials/person.jpg",
      rating: 5
    },
    {
      id: 2,
      name: "Ahmet YURDAKUL",
      position: "Data scientist",
      company: "HubX",
      testimonial: "The AI solution provided by Oyu Intelligence has automated several of our repetitive tasks, freeing up our team to focus on more strategic work. The implementation was seamless, and the support team has been fantastic in addressing any questions we&apos;ve had. This has been a game-changer for our productivity.",
      image: "/testimonials/data.jpg",
      rating: 5
    },
    {
      id: 3,
      name: "Uuganbayar",
      position: "Content Manager",
      company: "PrimeStore",
      testimonial: "Хамтарч ажиллахад тухай, гол нь маш чанартай үйлчилгээтэй байлаа. Чанарын хувьд Монголдоо байхааргүй өндөр чадварлаг залуус байна.",
      image: "/testimonials/3.png",
      rating: 5
    },
    {
      id: 4,
      name: "David Wilson",
      position: "Founder",
      company: "StartUp Ventures",
      testimonial: "As a startup founder, finding the right technology partner was crucial. Oyu Intelligence understood our vision and helped us build a solid foundation for our digital presence. Their expertise across multiple domains was invaluable.",
      image: "/testimonials/4.jpg",
      rating: 5
    },
  ];

  // Function to start auto-rotation
  const startAutoRotation = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    // Start progress bar animation
    if (progressBarRef.current) {
      const keyframes = [
        { width: '0%' },
        { width: '100%' }
      ];

      const options: KeyframeAnimationOptions = {
        duration: 7000, // 7 seconds for the progress bar
        easing: 'linear',
        fill: 'forwards'
      };

      progressAnimationRef.current = progressBarRef.current.animate(keyframes, options);
    }

    // Set interval for changing testimonials
    intervalRef.current = setInterval(() => {
      if (!isPaused && !isTransitioning) {
        setActiveIndex((prevIndex) => (prevIndex + 1) % testimonials.length);
      }
    }, 7000); // Change testimonial every 7 seconds
  }, [isPaused, isTransitioning, testimonials.length]);

  // Function to handle testimonial navigation
  const handleTestimonialChange = useCallback((index: number) => {
    if (isTransitioning) return;

    setIsTransitioning(true);
    setActiveIndex(index);

    // Reset the auto-rotation timer when manually changing testimonials
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    // Reset progress animation
    if (progressAnimationRef.current) {
      progressAnimationRef.current.cancel();
    }

    // Allow time for transition animation
    setTimeout(() => {
      setIsTransitioning(false);
      startAutoRotation();
    }, 600);
  }, [isTransitioning, startAutoRotation]);

  // Function to move to the next testimonial
  const goToNextTestimonial = useCallback(() => {
    handleTestimonialChange((activeIndex + 1) % testimonials.length);
  }, [activeIndex, testimonials.length, handleTestimonialChange]);

  // Function to move to the previous testimonial
  const goToPrevTestimonial = useCallback(() => {
    handleTestimonialChange((activeIndex - 1 + testimonials.length) % testimonials.length);
  }, [activeIndex, testimonials.length, handleTestimonialChange]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'ArrowRight') {
        goToNextTestimonial();
      } else if (e.key === 'ArrowLeft') {
        goToPrevTestimonial();
      }
    };

    window.addEventListener('keydown', handleKeyDown);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [goToNextTestimonial, goToPrevTestimonial]);

  // Start auto-rotation when component mounts and is in view
  useEffect(() => {
    if (isInView && !isPaused && !isTransitioning) {
      startAutoRotation();
    }

    // Clean up interval on unmount
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }

      if (progressAnimationRef.current) {
        progressAnimationRef.current.cancel();
      }
    };
  }, [isInView, isPaused, isTransitioning, startAutoRotation]);

  return (
    <section
      id="testimonials"
      ref={sectionRef}
      className="relative flex flex-col items-center justify-center py-20 px-4 overflow-hidden"
    >
      {/* Mouse event handler div - removed z-10 to prevent it from blocking clicks */}
      <div
        className="absolute inset-0"
        onMouseEnter={() => setIsPaused(true)}
        onMouseLeave={() => setIsPaused(false)}
        aria-hidden="true"
      >
      </div>

      {/* Background elements */}
      <div className="absolute top-1/4 right-0 w-72 h-72 bg-purple-900/20 rounded-full blur-3xl -z-10"></div>
      <div className="absolute bottom-1/4 left-0 w-72 h-72 bg-cyan-900/20 rounded-full blur-3xl -z-10"></div>

      <motion.div
        variants={slideInFromTop}
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
        className="Welcome-box py-[8px] px-[7px] border border-[#7042f88b] opacity-[0.9]] mb-6"
      >
        <h1 className="Welcome-text text-[13px]">
          Client Feedback
        </h1>
      </motion.div>

      <motion.h2
        variants={slideInFromLeft(0.5)}
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
        className="text-4xl font-bold text-white mb-4 text-center"
      >
        What Our <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-500 to-cyan-500">Clients Say</span>
      </motion.h2>

      <motion.p
        initial={{ opacity: 0, y: 20 }}
        animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
        transition={{ duration: 0.5, delay: 0.3 }}
        className="text-gray-400 text-center max-w-2xl mb-16"
      >
        Don&apos;t just take our word for it. Here&apos;s what our clients have to say about their experience working with Oyu Intelligence.
      </motion.p>

      {/* Testimonials carousel */}
      <div className="w-full max-w-7xl">
        {/* Desktop view - carousel */}
        <div className="hidden md:block relative">
          <div className="flex justify-center">
            <AnimatePresence mode="wait">
              <motion.div
                key={activeIndex}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                transition={{ duration: 0.5 }}
                className="w-full max-w-3xl"
              >
                <TestimonialCard
                  testimonial={testimonials[activeIndex]}
                  index={0}
                  isActive={true}
                />
              </motion.div>
            </AnimatePresence>
          </div>

          {/* Desktop navigation arrows - removed pointer-events-none from parent */}
          <div className="absolute top-1/2 left-0 right-0 -translate-y-1/2 flex justify-between px-4 z-20">
            <motion.button
              whileHover={{ scale: 1.1, backgroundColor: "rgba(30,10,60,0.8)" }}
              whileTap={{ scale: 0.9 }}
              onClick={goToPrevTestimonial}
              className="p-3 rounded-full bg-[rgba(30,10,60,0.5)] border border-[#7042F88B] shadow-lg hover:border-purple-500 transition-colors"
              aria-label="Previous testimonial"
              disabled={isTransitioning}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </motion.button>

            <motion.button
              whileHover={{ scale: 1.1, backgroundColor: "rgba(30,10,60,0.8)" }}
              whileTap={{ scale: 0.9 }}
              onClick={goToNextTestimonial}
              className="p-3 rounded-full bg-[rgba(30,10,60,0.5)] border border-[#7042F88B] shadow-lg hover:border-purple-500 transition-colors"
              aria-label="Next testimonial"
              disabled={isTransitioning}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </motion.button>
          </div>

          {/* Testimonial indicators */}
          <div className="flex justify-center mt-8 space-x-2">
            {testimonials.map((testimonial) => (
              <button
                key={`indicator-${testimonial.id}`}
                onClick={() => handleTestimonialChange(testimonial.id - 1)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  testimonial.id - 1 === activeIndex
                    ? "bg-gradient-to-r from-purple-500 to-cyan-500 w-6"
                    : "bg-gray-600 hover:bg-gray-500"
                }`}
                aria-label={`View testimonial ${testimonial.id}`}
                disabled={isTransitioning}
              />
            ))}
          </div>
        </div>

        {/* Mobile view - single card with animation */}
        <div className="md:hidden w-full">
          <AnimatePresence mode="wait">
            <motion.div
              key={activeIndex}
              initial={{ opacity: 0, x: 100 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -100 }}
              transition={{ duration: 0.5 }}
              className="w-full"
            >
              <TestimonialCard
                testimonial={testimonials[activeIndex]}
                index={0}
                isActive={true}
              />
            </motion.div>
          </AnimatePresence>

          {/* Navigation arrows for mobile */}
          <div className="flex justify-between mt-4 z-20">
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={goToPrevTestimonial}
              className="p-2 rounded-full bg-[rgba(30,10,60,0.5)] border border-[#7042F88B] hover:border-purple-500 transition-colors shadow-md"
              aria-label="Previous testimonial"
              disabled={isTransitioning}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </motion.button>

            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={goToNextTestimonial}
              className="p-2 rounded-full bg-[rgba(30,10,60,0.5)] border border-[#7042F88B] hover:border-purple-500 transition-colors shadow-md"
              aria-label="Next testimonial"
              disabled={isTransitioning}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </motion.button>
          </div>
        </div>
      </div>

      {/* Progress bar */}
      <div className="w-full max-w-md mt-10 relative h-1 bg-gray-700 rounded-full overflow-hidden">
        <div
          ref={progressBarRef}
          className="absolute top-0 left-0 h-full w-0 bg-gradient-to-r from-purple-500 to-cyan-500"
        />

        {/* Indicator dots */}
        <div className="absolute top-0 left-0 w-full h-full flex justify-between px-1">
          {testimonials.map((testimonial) => (
            <div
              key={`dot-${testimonial.id}`}
              className={`w-1 h-1 rounded-full mt-0 ${
                testimonial.id - 1 === activeIndex
                  ? "bg-white"
                  : "bg-gray-500"
              } transition-colors duration-300`}
            />
          ))}
        </div>
      </div>
    </section>
  );
};
