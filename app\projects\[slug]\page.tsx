import { ProjectDetailPage } from "@/components/pages/project-detail-page";
import { ALL_PROJECTS } from "@/constants";
import { notFound } from "next/navigation";

export async function generateStaticParams() {
  return ALL_PROJECTS.map((project) => ({
    slug: project.slug,
  }));
}

export default function ProjectDetail({ params }: { params: { slug: string } }) {
  const project = ALL_PROJECTS.find((p) => p.slug === params.slug);
  
  if (!project) {
    notFound();
  }
  
  return <ProjectDetailPage project={project} />;
}
