.orb-container {
  position: relative;
  z-index: 2;
  width: 100%;
  height: 100%;
}

.orb-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  color: white;
  font-size: 2.5rem;
  font-weight: bold;
  text-align: center;
  pointer-events: none;
  text-shadow: 0 0 20px rgba(147, 51, 234, 0.8), 0 0 40px rgba(147, 51, 234, 0.6);
  background: linear-gradient(45deg, #9333ea, #06b6d4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: textGlow 3s ease-in-out infinite alternate;
}

@keyframes textGlow {
  0% {
    text-shadow: 0 0 20px rgba(147, 51, 234, 0.8), 0 0 40px rgba(147, 51, 234, 0.6);
  }
  100% {
    text-shadow: 0 0 30px rgba(147, 51, 234, 1), 0 0 60px rgba(147, 51, 234, 0.8), 0 0 80px rgba(6, 182, 212, 0.6);
  }
}

@media (max-width: 768px) {
  .orb-text {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .orb-text {
    font-size: 1.5rem;
  }
}
