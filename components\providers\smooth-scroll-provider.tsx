"use client";

import { usePathname } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import type LocomotiveScroll from "locomotive-scroll";

interface SmoothScrollProviderProps {
  children: React.ReactNode;
}

export const SmoothScrollProvider = ({ children }: SmoothScrollProviderProps) => {
  const [locomotiveScroll, setLocomotiveScroll] = useState<LocomotiveScroll | null>(null);
  const scrollRef = useRef<HTMLDivElement>(null);
  const pathname = usePathname();

  useEffect(() => {
    // Only import and initialize on the client side
    (async () => {
      if (!locomotiveScroll) {
        const LocomotiveScroll = (await import("locomotive-scroll")).default;
        const scroll = new LocomotiveScroll({
          el: scrollRef.current as HTMLElement,
          smooth: true,
          smoothMobile: true, // Enable smooth scrolling on mobile
          resetNativeScroll: true,
          lerp: 0.05, // Lower value for smoother scrolling (was 0.075)
          multiplier: 0.8, // Slightly reduced for smoother feel (was 0.9)
          smartphone: {
            smooth: true, // Enable smooth scrolling on smartphones
            multiplier: 0.7, // Slower for better control on mobile
          },
          tablet: {
            smooth: true,
            breakpoint: 1024,
            multiplier: 0.75, // Smoother scrolling on tablets
          },
        });
        setLocomotiveScroll(scroll);
      }

      return () => {
        locomotiveScroll?.destroy();
      };
    })();
  }, [locomotiveScroll]);

  // Update scroll on route change
  useEffect(() => {
    if (locomotiveScroll) {
      // Small delay to ensure content is rendered
      setTimeout(() => {
        locomotiveScroll.update();
        // Scroll to top on page change
        locomotiveScroll.scrollTo(0, { duration: 0, disableLerp: true });
      }, 200);
    }
  }, [pathname, locomotiveScroll]);

  return (
    <div data-scroll-container ref={scrollRef}>
      {children}
    </div>
  );
};
