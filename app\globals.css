@import url("https://fonts.googleapis.com/css2?family=Cedarville+Cursive&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base styles for smoother animations */
* {
  @apply transition-colors duration-300 ease-in-out;
}

/* Enhanced 3D utilities */
@layer utilities {
  .perspective {
    perspective: 1000px;
  }

  .preserve-3d {
    transform-style: preserve-3d;
  }

  .backface-hidden {
    backface-visibility: hidden;
  }

  .rotate-y-180 {
    transform: rotateY(180deg);
  }

  /* Smooth animation utilities */
  .animate-smooth {
    @apply transition-all duration-500 ease-out;
  }

  .animate-bounce-soft {
    animation: bounce-soft 3s infinite ease-in-out;
  }

  .animate-float {
    animation: float 6s infinite ease-in-out;
  }

  .animate-pulse-soft {
    animation: pulse-soft 4s infinite ease-in-out;
  }

  .animate-gradient-xy {
    animation: gradient-xy 3s ease infinite;
  }

  .will-change-transform {
    will-change: transform;
  }

  .will-change-opacity {
    will-change: opacity;
  }
}

/* Animation keyframes */
@keyframes bounce-soft {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) rotate(0);
  }
  25% {
    transform: translateY(-8px) rotate(1deg);
  }
  50% {
    transform: translateY(0) rotate(0);
  }
  75% {
    transform: translateY(8px) rotate(-1deg);
  }
}

@keyframes pulse-soft {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(0.95);
  }
}

@keyframes gradient-xy {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@layer components {
  .button-primary {
    @apply bg-gradient-to-r from-purple-600 to-cyan-600 hover:opacity-90 transition-all duration-300 ease-out transform hover:scale-105 hover:shadow-lg hover:shadow-purple-500/20;
  }
}

.cursive {
  font-family: "Cedarville Cursive", cursive;
}
.Welcome-text {
  background: linear-gradient(
      0deg,
      rgba(255, 255, 255, 0.4),
      rgba(255, 255, 255, 0.4)
    ),
    linear-gradient(90.01deg, #e59cff 0.01%, #ba9cff 50.01%, #9cb2ff 100%);
  background-blend-mode: normal, screen;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}
.Welcome-box {
  isolation: isolate;
  overflow: hidden;
  align-items: center;
  justify-content: center;
  -webkit-backdrop-filter: blur(6px);
  backdrop-filter: blur(6px);
  border-radius: 32px;
  box-shadow: inset 0 -7px 11px #a48fff1f;
  display: flex;
  position: relative;
  width: -moz-max-content;
  width: max-content;
  transition: 0.45s cubic-bezier(0.6, 0.6, 0, 1) box-shadow;
}

.button-primary {
  background: linear-gradient(
      180deg,
      rgba(60, 8, 126, 0) 0%,
      rgba(60, 8, 126, 0.32) 100%
    ),
    rgba(113, 47, 255, 0.12);
  box-shadow: inset 0 0 12px #bf97ff3d;
}
.button-primary:hover {
  background: linear-gradient(
      180deg,
      rgba(60, 8, 126, 0) 0%,
      rgba(60, 8, 126, 0.42) 100%
    ),
    rgba(113, 47, 255, 0.24);
  box-shadow: inset 0 0 12px #bf97ff70;
}

/* Hide scrollbar for IE, Edge and Firefox */
.scrollbar-hidden {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
.scrollbar-hidden::-webkit-scrollbar {
  display: none;
}

html {
  scroll-behavior: smooth;
}

/* Custom cursor styles */
.cursor-none {
  cursor: none;
}

/* Optional class to hide cursor on specific elements if needed */
.hide-cursor {
  cursor: none;
}
