import { ProjectType } from "@/types";

export const ALL_PROJECTS: ProjectType[] = [
  {
    id: 9,
    slug: "lookluxe-ai-outfit-assistant",
    title: "LookLuxe AI Outfit Assistant",
    shortDescription: "AI-powered fashion application for personalized style recommendations and wardrobe management",
    description: "LookLuxe is a premium fashion application that leverages AI to deliver personalized style recommendations, outfit planning, and wardrobe management. The app combines advanced analytics and eco-conscious fashion insights to help users refine their personal style while making sustainable choices.\n\nKey features include weather integration with real-time forecasts for weather-appropriate outfit suggestions, context awareness for work/weekend mode and event-based recommendations, comprehensive style analysis with metrics like color coordination and trend alignment, AI camera analysis for real-time style feedback, and smart wardrobe organization with AI-powered categorization.\n\nThe app offers multiple revenue streams including a subscription model ($9.99/month or $49.99/year), 2-10% commission on clothing purchases from partner businesses, and income from paid partner brand notifications.",
    image: "/projects/lookluxe.jpg",
    link: "https://lookluxe.ai",
    category: "AI Automation",
    technologies: ["AI/ML", "React Native", "Firebase", "ChatGPT API", "Computer Vision", "Weather API", "Cloud Functions"],
    client: "LookLuxe Fashion Tech",
    completionDate: "April 2024",
    challenges: [
      "Developing accurate AI algorithms for style recommendations",
      "Creating a seamless user experience across multiple features",
      "Integrating real-time weather data with outfit suggestions",
      "Building an efficient wardrobe management system",
      "Implementing secure payment processing for subscriptions"
    ],
    solutions: [
      "Implemented advanced machine learning models for style analysis",
      "Designed an intuitive UI/UX with comprehensive user testing",
      "Created a robust weather API integration with caching mechanisms",
      "Developed an AI-powered categorization system for wardrobe items",
      "Integrated secure payment gateways with Firebase"
    ],
    results: [
      "Successful launch with 10,000+ downloads in the first month",
      "85% user retention rate after 30 days",
      "Average 4.7/5 rating on app stores",
      "25+ clothing brand partnerships established",
      "Featured in multiple fashion and technology publications"
    ],
    relatedProjects: [
      {
        slug: "astro-ai-chatbot-assistant",
        title: "Astro AI - Chatbot Assistant",
        image: "/projects/astro-ai.jpg",
        category: "AI Automation"
      },
      {
        slug: "neo-ai-logo-generator",
        title: "Neo AI - Logo Generator",
        image: "/projects/neo-ai.jpg",
        category: "AI Automation"
      }
    ]
  },
  {
    id: 10,
    slug: "astro-ai-chatbot-assistant",
    title: "Astro AI - Chatbot Assistant",
    shortDescription: "All-in-one next-generation AI assistant app with multiple LLMs and smart AI agents",
    description: "Astro AI is an all-in-one next-generation AI assistant app that brings together multiple powerful LLMs and a network of smart AI agents under a single subscription model. Designed for both utility and entertainment, Astro AI offers a personalized, multimodal, and highly intelligent experience through Agent-to-Agent (A-A) communication, persona-driven chatbots, and cross-platform support.\n\nCore innovations include unified LLM access (GPT-4o, Grok 3, DeepSeek, Claude Sonnet 3.7, Gemini 2.0–2.5, etc.), specialized AI agents for tasks like coding and content creation, Agent-to-Agent (A-A) communication for collaborative problem-solving, persona chatbots of public figures, native development for iOS and Android, and multimodal input support for text, voice, image, and video.\n\nThe app aims to make the world's best AI tools accessible through one platform, enhance productivity through automation and collaboration, and balance entertainment with intelligent functionality.",
    image: "/projects/astro-ai.jpg",
    link: "https://astro-ai.app",
    category: "AI Automation",
    technologies: ["Swift", "React Native", "LLM APIs", "AI Agents", "Natural Language Processing", "Cloud Infrastructure", "WebSockets"],
    client: "Astro Intelligence Inc.",
    completionDate: "May 2024",
    challenges: [
      "Integrating multiple LLM APIs with different requirements and capabilities",
      "Developing an efficient Agent-to-Agent communication protocol",
      "Creating realistic persona chatbots with appropriate boundaries",
      "Ensuring cross-platform consistency between iOS and Android",
      "Managing subscription access to multiple premium AI services"
    ],
    solutions: [
      "Built a unified API layer to standardize interactions with different LLMs",
      "Developed a proprietary protocol for inter-agent communication and task delegation",
      "Created sophisticated persona models with ethical guidelines and fact-checking",
      "Implemented platform-specific optimizations while maintaining feature parity",
      "Designed a flexible subscription management system with usage tracking"
    ],
    results: [
      "Successful beta launch with 5,000+ early access users",
      "90% positive feedback on Agent-to-Agent functionality",
      "Average session duration of 25+ minutes",
      "Partnerships with 5 leading AI model providers",
      "Featured in tech publications as a breakthrough AI assistant app"
    ],
    relatedProjects: [
      {
        slug: "lookluxe-ai-outfit-assistant",
        title: "LookLuxe AI Outfit Assistant",
        image: "/projects/lookluxe.jpg",
        category: "AI Automation"
      },
      {
        slug: "neo-ai-logo-generator",
        title: "Neo AI - Logo Generator",
        image: "/projects/neo-ai.jpg",
        category: "AI Automation"
      }
    ]
  },
  {
    id: 11,
    slug: "neo-ai-logo-generator",
    title: "Neo AI - Logo Generator",
    shortDescription: "AI-powered mobile app for generating custom logos and brand assets",
    description: "Neo AI is an innovative mobile application that leverages artificial intelligence to generate custom logos and brand assets for businesses and individuals. The app provides an intuitive interface for users to input their preferences, industry, and style requirements, then generates unique logo designs that can be further customized and exported in various formats.\n\nThe app uses advanced generative AI models specifically trained on design principles and brand aesthetics to create logos that are not only visually appealing but also appropriate for the user's industry and target audience. Users can generate multiple variations, make adjustments to colors, typography, and layout, and export their final designs in high-resolution formats suitable for both digital and print applications.\n\nNeo AI offers a range of pricing tiers from free basic generations to premium subscriptions with enhanced features, higher resolution exports, and commercial usage rights.",
    image: "/projects/neo-ai.jpg",
    link: "https://neo-ai.app",
    category: "AI Automation",
    technologies: ["React Native", "Generative AI", "Firebase", "Cloud Functions", "Vector Graphics", "In-App Purchases", "Design APIs"],
    client: "Neo Creative Technologies",
    completionDate: "March 2024",
    challenges: [
      "Training AI models to understand design principles and brand aesthetics",
      "Creating an intuitive interface for non-designers",
      "Generating high-quality vector graphics suitable for various applications",
      "Implementing efficient rendering and export functionality on mobile devices",
      "Balancing AI generation with user customization options"
    ],
    solutions: [
      "Developed custom AI models trained on professional logo designs and brand guidelines",
      "Designed a step-by-step interface with clear guidance and examples",
      "Implemented vector-based generation and editing capabilities",
      "Optimized rendering processes for mobile performance",
      "Created a hybrid approach combining AI generation with user-friendly editing tools"
    ],
    results: [
      "25,000+ app downloads since launch",
      "4.6/5 average rating on app stores",
      "Over 100,000 logos generated",
      "15% conversion rate from free to paid users",
      "Featured in design and technology publications"
    ],
    relatedProjects: [
      {
        slug: "lookluxe-ai-outfit-assistant",
        title: "LookLuxe AI Outfit Assistant",
        image: "/projects/lookluxe.jpg",
        category: "AI Automation"
      },
      {
        slug: "astro-ai-chatbot-assistant",
        title: "Astro AI - Chatbot Assistant",
        image: "/projects/astro-ai.jpg",
        category: "AI Automation"
      }
    ]
  },
  {
    id: 1,
    slug: "ai-customer-service",
    title: "AI-Powered Customer Service Platform",
    shortDescription: "Intelligent customer service platform with natural language processing capabilities",
    description: "We developed an intelligent customer service platform that uses natural language processing to automatically respond to customer inquiries, route complex issues to human agents, and provide analytics on customer satisfaction and common issues.\n\nThis AI-powered solution helped our client reduce response times by 75% while maintaining high customer satisfaction scores. The system continuously learns from interactions to improve its responses over time.\n\nThe platform includes a user-friendly dashboard for customer service managers to monitor performance metrics, identify trends, and optimize their team's workflow.",
    image: "/projects/project-1.png",
    link: "https://example.com/ai-customer-service",
    category: "AI Automation",
    technologies: ["Natural Language Processing", "Machine Learning", "Python", "TensorFlow", "React", "Node.js"],
    client: "TechCorp Solutions",
    completionDate: "March 2023",
    challenges: [
      "Processing and understanding a wide variety of customer inquiries",
      "Seamlessly transitioning between AI and human agents",
      "Ensuring data privacy and security compliance",
      "Developing an intuitive interface for customer service managers"
    ],
    solutions: [
      "Implemented advanced NLP algorithms to understand customer intent",
      "Created a sophisticated routing system based on query complexity and sentiment analysis",
      "Developed a secure data handling framework with end-to-end encryption",
      "Designed an intuitive dashboard with real-time analytics and visualization"
    ],
    results: [
      "75% reduction in average response time",
      "30% decrease in customer service operational costs",
      "92% customer satisfaction rating",
      "Ability to handle 5,000+ simultaneous customer inquiries"
    ],
    relatedProjects: [
      {
        slug: "predictive-analytics-dashboard",
        title: "Predictive Analytics Dashboard",
        image: "/projects/project-4.png",
        category: "AI Automation"
      },
      {
        slug: "healthcare-mobile-app",
        title: "Healthcare Mobile App",
        image: "/projects/project-2.png",
        category: "Mobile App"
      }
    ]
  },
  {
    id: 2,
    slug: "healthcare-mobile-app",
    title: "Cross-Platform Mobile App for Healthcare",
    shortDescription: "Comprehensive healthcare mobile application for patient management and communication",
    description: "Our team created a comprehensive healthcare mobile application that allows patients to schedule appointments, access medical records, communicate with healthcare providers, and receive medication reminders - all with a seamless user experience across iOS and Android devices.\n\nThe app integrates with various healthcare systems through secure APIs, ensuring that patient data is always up-to-date and accessible when needed. We implemented strict security measures to comply with healthcare regulations and protect sensitive patient information.\n\nThe intuitive interface makes it easy for users of all ages to navigate the app, while the backend systems ensure reliable performance even during peak usage times.",
    image: "/projects/project-2.png",
    link: "https://example.com/healthcare-app",
    category: "Mobile App",
    technologies: ["React Native", "Firebase", "Node.js", "Express", "MongoDB", "OAuth 2.0"],
    client: "MediCare Health Services",
    completionDate: "August 2023",
    challenges: [
      "Ensuring HIPAA compliance and data security",
      "Integrating with multiple healthcare provider systems",
      "Creating an intuitive interface for users of all ages",
      "Implementing reliable push notifications for medication reminders"
    ],
    solutions: [
      "Developed a secure architecture with end-to-end encryption",
      "Created flexible API adapters for different healthcare systems",
      "Conducted extensive user testing with diverse age groups",
      "Implemented a reliable notification system with offline capabilities"
    ],
    results: [
      "40% increase in appointment scheduling efficiency",
      "95% patient satisfaction rating",
      "28% reduction in missed appointments",
      "Successful deployment across 12 healthcare facilities"
    ],
    relatedProjects: [
      {
        slug: "ai-customer-service",
        title: "AI-Powered Customer Service Platform",
        image: "/projects/project-1.png",
        category: "AI Automation"
      },
      {
        slug: "fitness-tracking-app",
        title: "Fitness Tracking App",
        image: "/projects/project-5.png",
        category: "Mobile App"
      }
    ]
  },
  {
    id: 3,
    slug: "ar-ecommerce",
    title: "E-Commerce Website with AR Integration",
    shortDescription: "Modern e-commerce platform with augmented reality features for product visualization",
    description: "We designed and developed a modern e-commerce platform with augmented reality features that allow customers to visualize products in their own space before purchasing, resulting in a 35% increase in conversion rates and significantly reduced return rates.\n\nThe platform includes a sophisticated product recommendation engine that uses machine learning to suggest relevant items based on browsing history, purchase patterns, and similar customer profiles.\n\nThe AR functionality works seamlessly across both mobile and desktop devices, providing an immersive shopping experience that helps customers make more confident purchasing decisions.",
    image: "/projects/project-3.png",
    link: "https://example.com/ar-ecommerce",
    category: "Web Development",
    technologies: ["React", "Three.js", "WebGL", "Node.js", "MongoDB", "AWS", "WebAR"],
    client: "LuxHome Furnishings",
    completionDate: "November 2023",
    challenges: [
      "Creating realistic AR representations of products",
      "Ensuring cross-device compatibility",
      "Optimizing performance for 3D rendering",
      "Integrating with existing inventory management systems"
    ],
    solutions: [
      "Developed high-quality 3D models with accurate textures and lighting",
      "Implemented responsive design with device-specific optimizations",
      "Created efficient loading and rendering processes",
      "Built custom API connectors for seamless inventory synchronization"
    ],
    results: [
      "35% increase in conversion rate",
      "42% reduction in product return rate",
      "28% increase in average order value",
      "65% of customers using AR features before purchase"
    ],
    relatedProjects: [
      {
        slug: "corporate-website-redesign",
        title: "Corporate Website Redesign",
        image: "/projects/project-6.png",
        category: "Web Development"
      },
      {
        slug: "social-media-dashboard",
        title: "Social Media Management Dashboard",
        image: "/projects/project-7.png",
        category: "Social Media"
      }
    ]
  },
  {
    id: 4,
    slug: "predictive-analytics-dashboard",
    title: "Predictive Analytics Dashboard",
    shortDescription: "Advanced analytics platform with predictive capabilities for business intelligence",
    description: "We created a sophisticated predictive analytics dashboard that helps businesses forecast trends, identify opportunities, and make data-driven decisions. The platform integrates data from multiple sources and uses advanced machine learning algorithms to provide accurate predictions and actionable insights.\n\nThe dashboard features interactive visualizations that make complex data easy to understand, with customizable reports and alerts that can be tailored to specific business needs.\n\nReal-time data processing ensures that decision-makers always have access to the most current information, while historical analysis helps identify long-term trends and patterns.",
    image: "/projects/project-4.png",
    link: "https://example.com/predictive-analytics",
    category: "AI Automation",
    technologies: ["Python", "TensorFlow", "React", "D3.js", "PostgreSQL", "Docker", "Kubernetes"],
    client: "Global Analytics Inc.",
    completionDate: "January 2024",
    challenges: [
      "Processing and analyzing large volumes of data efficiently",
      "Creating accurate predictive models for diverse business scenarios",
      "Designing intuitive visualizations for complex data",
      "Ensuring system scalability for growing data needs"
    ],
    solutions: [
      "Implemented distributed computing architecture for efficient data processing",
      "Developed ensemble machine learning models for improved prediction accuracy",
      "Created customizable visualization components with user experience focus",
      "Built cloud-native infrastructure with automatic scaling capabilities"
    ],
    results: [
      "22% improvement in forecast accuracy",
      "15% reduction in operational costs through data-driven decisions",
      "40% faster identification of market opportunities",
      "Successful deployment across 5 departments with 200+ active users"
    ]
  },
  {
    id: 5,
    slug: "fitness-tracking-app",
    title: "Fitness Tracking App",
    shortDescription: "Comprehensive fitness application with personalized workout plans and progress tracking",
    description: "We developed a feature-rich fitness tracking application that provides users with personalized workout plans, nutrition guidance, and progress tracking. The app uses machine learning to adapt recommendations based on user performance and goals.\n\nThe application includes social features that allow users to connect with friends, join challenges, and share achievements, creating a motivating community environment.\n\nAdvanced analytics help users understand their fitness journey with detailed insights into performance trends, milestone achievements, and areas for improvement.",
    image: "/projects/project-5.png",
    link: "https://example.com/fitness-app",
    category: "Mobile App",
    technologies: ["Flutter", "Firebase", "TensorFlow Lite", "Node.js", "MongoDB", "Google Fit API", "Apple HealthKit"],
    client: "FitLife Innovations",
    completionDate: "October 2023",
    challenges: [
      "Creating accurate exercise recognition algorithms",
      "Developing personalized recommendation systems",
      "Ensuring battery efficiency with continuous tracking",
      "Synchronizing data across multiple devices and platforms"
    ],
    solutions: [
      "Implemented computer vision and motion sensing for exercise recognition",
      "Developed adaptive recommendation engine based on user progress",
      "Optimized background processes for minimal battery consumption",
      "Created robust synchronization system with offline capabilities"
    ],
    results: [
      "500,000+ downloads in first three months",
      "4.8/5 average rating on app stores",
      "85% user retention rate after 30 days",
      "32% average improvement in user fitness metrics"
    ]
  },
  {
    id: 6,
    slug: "corporate-website-redesign",
    title: "Corporate Website Redesign",
    shortDescription: "Modern, responsive website redesign for a multinational corporation",
    description: "We completely redesigned and developed a corporate website for a multinational company, focusing on modern design principles, responsive layouts, and optimized user journeys. The new website significantly improved user engagement metrics and conversion rates.\n\nThe site features a content management system that makes it easy for non-technical staff to update content, manage blog posts, and publish press releases without developer assistance.\n\nAdvanced SEO optimization and performance enhancements resulted in improved search engine rankings and faster page load times across all devices.",
    image: "/projects/project-6.png",
    link: "https://example.com/corporate-website",
    category: "Web Development",
    technologies: ["Next.js", "TypeScript", "Tailwind CSS", "Strapi CMS", "GraphQL", "AWS", "Cloudflare"],
    client: "Global Enterprises Ltd.",
    completionDate: "December 2023",
    challenges: [
      "Migrating large volumes of content from legacy systems",
      "Creating a consistent experience across multiple languages and regions",
      "Optimizing performance for global audience",
      "Implementing robust security measures for corporate data"
    ],
    solutions: [
      "Developed automated content migration tools with validation",
      "Implemented internationalization framework with region-specific customizations",
      "Created global CDN architecture with edge caching",
      "Established comprehensive security protocols with regular auditing"
    ],
    results: [
      "68% improvement in page load speed",
      "42% increase in average session duration",
      "53% growth in organic search traffic",
      "27% higher conversion rate for lead generation forms"
    ]
  },
  {
    id: 7,
    slug: "social-media-dashboard",
    title: "Social Media Management Dashboard",
    shortDescription: "Comprehensive social media management platform for brands and agencies",
    description: "We built a powerful social media management dashboard that allows brands and agencies to plan, schedule, publish, and analyze content across multiple social platforms from a single interface. The system includes advanced analytics and reporting features.\n\nThe platform includes AI-powered content suggestions, optimal posting time recommendations, and sentiment analysis of audience engagement to help users maximize their social media effectiveness.\n\nComprehensive competitor analysis tools provide insights into industry trends and help users benchmark their performance against similar accounts in their niche.",
    image: "/projects/project-7.png",
    link: "https://example.com/social-dashboard",
    category: "Social Media",
    technologies: ["React", "Redux", "Node.js", "Express", "MongoDB", "Social Media APIs", "Chart.js"],
    client: "Digital Marketing Solutions",
    completionDate: "February 2024",
    challenges: [
      "Integrating with multiple social media platform APIs",
      "Processing and analyzing large volumes of social data",
      "Creating intuitive scheduling and publishing workflows",
      "Developing meaningful analytics visualizations"
    ],
    solutions: [
      "Built flexible API integration layer with fallback mechanisms",
      "Implemented efficient data processing pipeline with caching",
      "Designed user-centered workflows based on extensive research",
      "Created customizable dashboard with actionable insights"
    ],
    results: [
      "85% reduction in time spent on social media management",
      "32% improvement in engagement rates",
      "45% increase in content consistency",
      "Successful onboarding of 150+ agencies and brands"
    ]
  },
  {
    id: 8,
    slug: "blockchain-supply-chain",
    title: "Blockchain Supply Chain Solution",
    shortDescription: "Transparent and secure supply chain management system using blockchain technology",
    description: "We developed an innovative supply chain management system using blockchain technology to provide unprecedented transparency, traceability, and security throughout the entire supply chain process. The solution helps prevent fraud, verify authenticity, and improve efficiency.\n\nThe platform allows all stakeholders to track products from origin to consumer, with immutable records of every transaction and transfer of custody. Smart contracts automate compliance and payment processes based on predefined conditions.\n\nAdvanced analytics provide insights into supply chain efficiency, identifying bottlenecks and opportunities for optimization while ensuring regulatory compliance.",
    image: "/projects/project-8.png",
    link: "https://example.com/blockchain-supply",
    category: "AI Automation",
    technologies: ["Ethereum", "Solidity", "React", "Node.js", "IPFS", "AWS", "IoT Integration"],
    client: "Global Supply Solutions",
    completionDate: "September 2023",
    challenges: [
      "Ensuring interoperability with existing supply chain systems",
      "Creating user-friendly interfaces for blockchain interaction",
      "Developing secure and efficient smart contracts",
      "Integrating IoT devices for automated tracking"
    ],
    solutions: [
      "Built flexible middleware for legacy system integration",
      "Designed intuitive interfaces that abstract blockchain complexity",
      "Implemented thoroughly audited smart contracts with fail-safes",
      "Created secure IoT gateway for device authentication and data validation"
    ],
    results: [
      "62% improvement in supply chain transparency",
      "43% reduction in fraud incidents",
      "28% decrease in administrative overhead",
      "35% faster dispute resolution"
    ]
  }
];
