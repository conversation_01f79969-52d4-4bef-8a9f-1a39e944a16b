export type ProjectType = {
  id: number;
  slug: string;
  title: string;
  description: string;
  shortDescription?: string;
  image: string;
  link: string;
  category: string;
  technologies: string[];
  client: string;
  completionDate: string;
  challenges?: string[];
  solutions?: string[];
  results?: string[];
  relatedProjects?: {
    slug: string;
    title: string;
    image: string;
    category: string;
  }[];
};

export type TestimonialType = {
  id: number;
  name: string;
  position: string;
  company: string;
  testimonial: string;
  image: string;
  rating: number;
};

export type BlogPostType = {
  id: number;
  slug: string;
  title: string;
  excerpt: string;
  content: string;
  image: string;
  author: string;
  authorImage: string;
  date: string;
  category: string;
  readTime: string;
};

export type NavLinkType = {
  title: string;
  link: string;
};
