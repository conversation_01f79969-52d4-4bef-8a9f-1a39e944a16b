"use client";

import { ChevronDownIcon } from "@heroicons/react/24/outline";
import { AnimatePresence, motion, useInView } from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import { useRef, useState } from "react";

import { slideInFromLeft, slideInFromRight, slideInFromTop } from "@/lib/motion";

// Team member component
const TeamMember = ({
  member,
  index
}: {
  member: {
    id: number;
    name: string;
    position: string;
    bio: string;
    image: string;
    socials: {
      platform: string;
      url: string;
      icon: string;
    }[];
  };
  index: number;
}) => {
  const memberRef = useRef(null);
  const isInView = useInView(memberRef, { once: true, amount: 0.2 });
  const [isHovered, setIsHovered] = useState(false);

  return (
    <motion.div
      ref={memberRef}
      initial={{ opacity: 0, y: 50 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      className="relative group"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="bg-[rgba(3,0,20,0.5)] rounded-xl border border-[#7042F88B] overflow-hidden group-hover:border-purple-500 transition-all duration-300 h-full">
        {/* Image container with overlay */}
        <div className="relative h-80 w-full overflow-hidden">
          <Image
            src={member.image}
            alt={member.name}
            fill
            className="object-cover transition-transform duration-500 group-hover:scale-110"
          />

          {/* Overlay with social icons */}
          <div className={`absolute inset-0 bg-gradient-to-t from-[rgba(3,0,20,0.9)] to-transparent flex items-end justify-center p-4 transition-opacity duration-300 ${isHovered ? 'opacity-100' : 'opacity-0'}`}>
            <div className="flex space-x-3">
              {member.socials.map((social) => (
                <a
                  key={social.platform}
                  href={social.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-10 h-10 rounded-full bg-[rgba(30,10,60,0.7)] flex items-center justify-center hover:bg-purple-600 transition-colors"
                  aria-label={`${member.name}'s ${social.platform}`}
                >
                  <Image
                    src={social.icon}
                    alt={social.platform}
                    width={20}
                    height={20}
                  />
                </a>
              ))}
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          <h3 className="text-xl font-bold text-white mb-1">{member.name}</h3>
          <p className="text-purple-400 mb-4">{member.position}</p>
          <p className="text-gray-400 text-sm line-clamp-3">{member.bio}</p>
        </div>
      </div>
    </motion.div>
  );
};

// Process step component
const ProcessStep = ({
  step,
  index
}: {
  step: {
    number: number;
    title: string;
    description: string;
    icon: string;
  };
  index: number;
}) => {
  const stepRef = useRef(null);
  const isInView = useInView(stepRef, { once: true, amount: 0.2 });

  return (
    <motion.div
      ref={stepRef}
      initial={{ opacity: 0, y: 50 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
      transition={{ duration: 0.5, delay: index * 0.2 }}
      className="relative"
    >
      {/* Connector line for all but the last item */}
      {index < 3 && (
        <div className="absolute top-16 left-1/2 w-full h-px bg-gradient-to-r from-purple-500 to-cyan-500 transform -translate-x-1/2 hidden md:block"></div>
      )}

      <div className="flex flex-col items-center text-center relative z-10">
        {/* Step number with icon */}
        <div className="w-32 h-32 rounded-full bg-gradient-to-br from-purple-600 to-cyan-600 flex items-center justify-center mb-6 relative">
          <div className="absolute inset-1 rounded-full bg-[rgba(3,0,20,0.8)] flex items-center justify-center">
            <Image
              src={step.icon}
              alt={`Step ${step.number}`}
              width={48}
              height={48}
            />
          </div>
          <div className="absolute -top-2 -right-2 w-10 h-10 rounded-full bg-[#030014] border-2 border-purple-500 flex items-center justify-center text-white font-bold">
            {step.number}
          </div>
        </div>

        {/* Content */}
        <h3 className="text-xl font-bold text-white mb-3">{step.title}</h3>
        <p className="text-gray-400 max-w-xs">{step.description}</p>
      </div>
    </motion.div>
  );
};

// FAQ item component
const FAQItem = ({
  faq,
  index,
  isOpen,
  toggleOpen
}: {
  faq: {
    question: string;
    answer: string;
  };
  index: number;
  isOpen: boolean;
  toggleOpen: () => void;
}) => {
  const faqRef = useRef(null);
  const isInView = useInView(faqRef, { once: true, amount: 0.1 });

  return (
    <motion.div
      ref={faqRef}
      initial={{ opacity: 0, y: 20 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
      transition={{ duration: 0.4, delay: index * 0.1 }}
      className="border-b border-[#7042F88B] last:border-b-0"
    >
      <button
        onClick={toggleOpen}
        className="w-full py-6 flex items-center justify-between text-left focus:outline-none"
      >
        <h3 className="text-lg font-medium text-white pr-8">{faq.question}</h3>
        <ChevronDownIcon
          className={`h-5 w-5 text-purple-400 transition-transform duration-300 ${isOpen ? 'transform rotate-180' : ''}`}
        />
      </button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            <div className="pb-6 text-gray-400">
              {faq.answer}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export const CompanyPage = () => {
  const [openFAQ, setOpenFAQ] = useState<number | null>(0);
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, { once: true, amount: 0.1 });

  // Team members data
  const teamMembers = [
    {
      id: 1,
      name: "Boldbat Khuukhenduu",
      position: "CEO & Founder",
      bio: "Alexander brings over 15 years of experience in technology leadership and innovation. He founded Oyu Intelligence with a vision to transform how businesses leverage AI and digital solutions.",
      image: "/team/ceo.jpg",
      socials: [
        { platform: "LinkedIn", url: "https://www.linkedin.com/in/boldbat/", icon: "/icons/linkedin.svg" },
        { platform: "Twitter", url: "https://www.boldbat.info/", icon: "/icons/twitter.svg" }
      ]
    },
    {
      id: 2,
      name: "Sophia Chen",
      position: "CTO",
      bio: "Sophia leads our technical strategy and innovation initiatives. With a background in AI research and enterprise software development, she ensures our solutions leverage cutting-edge technology.",
      image: "/team/ceo.jpg",
      socials: [
        { platform: "LinkedIn", url: "https://linkedin.com", icon: "/icons/linkedin.svg" },
        { platform: "GitHub", url: "https://github.com", icon: "/icons/github.svg" }
      ]
    },
    {
      id: 3,
      name: "Marcus Johnson",
      position: "Creative Director",
      bio: "Marcus oversees all design and creative aspects of our projects. His expertise in UX/UI design and brand strategy helps create exceptional digital experiences for our clients.",
      image: "/team/ceo.jpg",
      socials: [
        { platform: "LinkedIn", url: "https://linkedin.com", icon: "/icons/linkedin.svg" },
        { platform: "Dribbble", url: "https://dribbble.com", icon: "/icons/dribbble.svg" }
      ]
    },
    {
      id: 4,
      name: "Elena Rodriguez",
      position: "Head of AI Solutions",
      bio: "Elena specializes in developing AI automation solutions that transform business operations. Her background in machine learning and data science drives our AI innovation.",
      image: "/team/ceo.jpg",
      socials: [
        { platform: "LinkedIn", url: "https://linkedin.com", icon: "/icons/linkedin.svg" },
        { platform: "Twitter", url: "https://twitter.com", icon: "/icons/twitter.svg" }
      ]
    }
  ];

  // Process steps data
  const processSteps = [
    {
      number: 1,
      title: "Discovery & Planning",
      description: "We begin by understanding your business needs, goals, and challenges through in-depth consultation and research.",
      icon: "/icons/discovery.svg"
    },
    {
      number: 2,
      title: "Strategy & Design",
      description: "Our team develops a comprehensive strategy and creates detailed designs tailored to your specific requirements.",
      icon: "/icons/strategy.svg"
    },
    {
      number: 3,
      title: "Development & Testing",
      description: "We build your solution using cutting-edge technologies and conduct rigorous testing to ensure quality and performance.",
      icon: "/icons/development.svg"
    },
    {
      number: 4,
      title: "Deployment & Support",
      description: "After successful deployment, we provide ongoing support and optimization to ensure long-term success.",
      icon: "/icons/support.svg"
    }
  ];

  // FAQ data
  const faqs = [
    {
      question: "What industries do you specialize in?",
      answer: "We work with clients across various industries including healthcare, finance, retail, education, and technology. Our solutions are adaptable to different business contexts, and we have experience addressing the unique challenges of each sector."
    },
    {
      question: "How long does a typical project take to complete?",
      answer: "Project timelines vary depending on scope and complexity. A simple website might take 4-6 weeks, while a comprehensive mobile app or AI solution could take 3-6 months. During our discovery phase, we'll provide a detailed timeline specific to your project."
    },
    {
      question: "Do you offer ongoing maintenance and support?",
      answer: "Yes, we offer various maintenance and support packages to ensure your solution continues to perform optimally after launch. These can include regular updates, performance monitoring, security patches, and technical support."
    },
    {
      question: "How do you approach data security and privacy?",
      answer: "We prioritize data security and privacy in all our solutions. We implement industry best practices, comply with relevant regulations (GDPR, CCPA, etc.), use encryption for sensitive data, and conduct regular security audits to protect your information and that of your users."
    },
    {
      question: "What is your pricing model?",
      answer: "We offer flexible pricing models including project-based, hourly, and retainer options. The specific approach depends on your project needs. We provide detailed proposals with transparent pricing after our initial consultation and requirements gathering."
    }
  ];

  // Toggle FAQ open/closed
  const toggleFAQ = (index: number) => {
    setOpenFAQ(openFAQ === index ? null : index);
  };

  return (
    <div className="flex flex-col min-h-screen py-20 px-4 mt-16">
      <div className="max-w-7xl mx-auto w-full">
        {/* Company Header */}
        <motion.div
          ref={sectionRef}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-20"
        >
          <motion.div
            variants={slideInFromTop}
            initial="hidden"
            animate={isInView ? "visible" : "hidden"}
            className="Welcome-box py-[8px] px-[7px] border border-[#7042f88b] opacity-[0.9]] mb-6 mx-auto w-fit"
          >
            <h1 className="Welcome-text text-[13px]">
              Our Company
            </h1>
          </motion.div>

          <motion.h1
            variants={slideInFromLeft(0.3)}
            initial="hidden"
            animate={isInView ? "visible" : "hidden"}
            className="text-4xl font-bold text-white mb-4"
          >
            About <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-500 to-cyan-500">Oyu Intelligence</span>
          </motion.h1>

          <motion.p
            variants={slideInFromRight(0.5)}
            initial="hidden"
            animate={isInView ? "visible" : "hidden"}
            className="text-gray-400 max-w-3xl mx-auto"
          >
            We are a Mongolian Start-Up company with a team of passionate technologists dedicated to transforming businesses through innovative digital solutions.
            Based in Ulaanbaatar, our expertise spans AI automation, mobile development, web design, and social media strategy.
            With over 100 successful projects, 20+ clients, and 3 brand partners, we&apos;re committed to delivering excellence.
          </motion.p>
        </motion.div>

        {/* Team Section */}
        <section className="mb-24">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">Meet Our Team</h2>
            <p className="text-gray-400 max-w-2xl mx-auto">
              Our talented team brings together diverse expertise and perspectives to deliver exceptional results for our clients.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {teamMembers.map((member, index) => (
              <TeamMember key={member.id} member={member} index={index} />
            ))}
          </div>
        </section>

        {/* Process Section */}
        <section className="mb-24">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-white mb-4">How We Work</h2>
            <p className="text-gray-400 max-w-2xl mx-auto">
              Our 4-stage process ensures we deliver high-quality solutions that meet your business needs.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {processSteps.map((step, index) => (
              <ProcessStep key={step.number} step={step} index={index} />
            ))}
          </div>
        </section>

        {/* FAQ Section */}
        <section className="mb-24">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">Frequently Asked Questions</h2>
            <p className="text-gray-400 max-w-2xl mx-auto">
              Find answers to common questions about our services and process.
            </p>
          </div>

          <div className="max-w-3xl mx-auto bg-[rgba(3,0,20,0.5)] border border-[#7042F88B] rounded-xl p-6">
            {faqs.map((faq, index) => (
              <FAQItem
                key={index}
                faq={faq}
                index={index}
                isOpen={openFAQ === index}
                toggleOpen={() => toggleFAQ(index)}
              />
            ))}
          </div>
        </section>

        {/* CTA Section */}
        <section className="bg-gradient-to-r from-purple-900/20 to-cyan-900/20 rounded-xl p-12 text-center">
          <h2 className="text-2xl font-bold text-white mb-4">Ready to work with us?</h2>
          <p className="text-gray-300 mb-8 max-w-2xl mx-auto">
            Lets discuss how Oyu Intelligence can help bring your vision to life with our expertise and innovative solutions.
          </p>
          <Link
            href="/#contact"
            className="px-8 py-3 button-primary text-white rounded-lg hover:opacity-90 transition-opacity inline-block"
          >
            Contact Us
          </Link>
        </section>
      </div>
    </div>
  );
};
