// Enhanced motion variants with smoother transitions
export function slideInFromLeft(delay: number) {
  return {
    hidden: { x: -100, opacity: 0, filter: "blur(8px)" },
    visible: {
      x: 0,
      opacity: 1,
      filter: "blur(0px)",
      transition: {
        delay: delay,
        duration: 0.7,
        ease: [0.25, 0.1, 0.25, 1], // Improved easing function
      },
    },
  };
}

export function slideInFromRight(delay: number) {
  return {
    hidden: { x: 100, opacity: 0, filter: "blur(8px)" },
    visible: {
      x: 0,
      opacity: 1,
      filter: "blur(0px)",
      transition: {
        delay: delay,
        duration: 0.7,
        ease: [0.25, 0.1, 0.25, 1], // Improved easing function
      },
    },
  };
}

export const slideInFromTop = {
  hidden: { y: -100, opacity: 0, filter: "blur(8px)" },
  visible: {
    y: 0,
    opacity: 1,
    filter: "blur(0px)",
    transition: {
      delay: 0.5,
      duration: 0.7,
      ease: [0.25, 0.1, 0.25, 1], // Improved easing function
    },
  },
};

// New animations for enhanced UI
export const fadeIn = (delay: number = 0, duration: number = 0.7) => ({
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      delay,
      duration,
      ease: [0.25, 0.1, 0.25, 1],
    },
  },
});

export const scaleUp = (delay: number = 0, duration: number = 0.7) => ({
  hidden: { scale: 0.9, opacity: 0 },
  visible: {
    scale: 1,
    opacity: 1,
    transition: {
      delay,
      duration,
      ease: [0.34, 1.56, 0.64, 1], // Spring-like easing
    },
  },
});

export const staggerContainer = (staggerChildren: number = 0.1, delayChildren: number = 0) => ({
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren,
      delayChildren,
    },
  },
});

// Smooth page transition variants
export const pageVariants = {
  initial: {
    opacity: 0,
    y: 20,
  },
  animate: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: [0.25, 0.1, 0.25, 1],
    },
  },
  exit: {
    opacity: 0,
    y: -20,
    transition: {
      duration: 0.4,
      ease: [0.25, 0.1, 0.25, 1],
    },
  },
};
