"use client";

import {
  motion,
  useScroll,
  useTransform,
  useInView,
} from "framer-motion";
import Image from "next/image";
import { useRef, useState } from "react";

import { slideInFromTop } from "@/lib/motion";

// Animated counter component
const AnimatedCounter = ({
  value,
  label,
  color,
  delay
}: {
  value: number;
  label: string;
  color: string;
  delay: number;
}) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, amount: 0.5 });
  const [displayValue, setDisplayValue] = useState(0);

  // Animate the counter when in view
  if (isInView && displayValue !== value) {
    setTimeout(() => {
      const interval = setInterval(() => {
        setDisplayValue(prev => {
          const newValue = prev + 1;
          if (newValue >= value) {
            clearInterval(interval);
            return value;
          }
          return newValue;
        });
      }, 30);

      return () => clearInterval(interval);
    }, delay);
  }

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 20, scale: 0.9 }}
      animate={isInView ? { opacity: 1, y: 0, scale: 1 } : { opacity: 0, y: 20, scale: 0.9 }}
      transition={{ duration: 0.5, delay: delay / 1000 }}
      className="bg-[rgba(3,0,20,0.5)] border border-[#7042F88B] rounded-lg p-4 flex items-center justify-center flex-col w-[120px] relative overflow-hidden group"
    >
      {/* Animated background glow */}
      <div
        className={`absolute inset-0 opacity-0 group-hover:opacity-20 transition-opacity duration-700 bg-gradient-to-r ${
          color === "purple" ? "from-purple-600 to-purple-400" : "from-cyan-600 to-cyan-400"
        }`}
      />

      {/* Animated number */}
      <motion.span
        className={`text-3xl font-bold ${color === "purple" ? "text-purple-500" : "text-cyan-500"}`}
        initial={{ opacity: 0, y: 10 }}
        animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 10 }}
        transition={{ duration: 0.3, delay: delay / 1000 }}
      >
        {displayValue}+
      </motion.span>

      {/* Label */}
      <motion.span
        className="text-gray-300 text-sm"
        initial={{ opacity: 0 }}
        animate={isInView ? { opacity: 1 } : { opacity: 0 }}
        transition={{ duration: 0.3, delay: (delay + 300) / 1000 }}
      >
        {label}
      </motion.span>
    </motion.div>
  );
};

// Animated text with character-by-character reveal
const AnimatedText = ({
  text,
  className,
  delay = 0
}: {
  text: string;
  className: string;
  delay?: number;
}) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, amount: 0.5 });

  const words = text.split(' ');

  return (
    <motion.p
      ref={ref}
      className={className}
    >
      {words.map((word, wordIndex) => (
        <span key={`word-${word}-${wordIndex}`} className="inline-block mr-1">
          {word.split('').map((char, charIndex) => (
            <motion.span
              key={`char-${word}-${wordIndex}-${charIndex}`}
              initial={{ opacity: 0, y: 15 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 15 }}
              transition={{
                duration: 0.3,
                delay: delay + (wordIndex * 0.05) + (charIndex * 0.02),
                ease: "easeOut"
              }}
              className="inline-block"
            >
              {char}
            </motion.span>
          ))}
          {' '}
        </span>
      ))}
    </motion.p>
  );
};

export const About = () => {
  const sectionRef = useRef(null);
  const imageRef = useRef(null);
  const isInView = useInView(sectionRef, { once: true, amount: 0.2 });
  const imageInView = useInView(imageRef, { once: true });

  // Parallax effect for the image
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start end", "end start"]
  });

  const y = useTransform(scrollYProgress, [0, 1], [-50, 50]);
  const scale = useTransform(scrollYProgress, [0, 0.5, 1], [0.9, 1.05, 1]);

  return (
    <section
      id="about-us"
      ref={sectionRef}
      className="relative flex flex-col items-center justify-center py-20 px-4 overflow-hidden"
    >
      {/* Background elements */}
      <motion.div
        className="absolute top-20 left-0 w-72 h-72 bg-purple-900/10 rounded-full blur-3xl"
        style={{
          x: useTransform(scrollYProgress, [0, 1], [-100, 100]),
          opacity: useTransform(scrollYProgress, [0, 0.5, 1], [0.1, 0.3, 0.1])
        }}
      />

      <motion.div
        className="absolute bottom-20 right-0 w-72 h-72 bg-cyan-900/10 rounded-full blur-3xl"
        style={{
          x: useTransform(scrollYProgress, [0, 1], [100, -100]),
          opacity: useTransform(scrollYProgress, [0, 0.5, 1], [0.1, 0.3, 0.1])
        }}
      />

      {/* Section title */}
      <motion.div
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
        variants={{
          hidden: { opacity: 0 },
          visible: { opacity: 1 }
        }}
        transition={{ duration: 0.5 }}
      >
        <motion.div
          variants={slideInFromTop}
          className="Welcome-box py-[8px] px-[10px] border border-[#7042f88b] opacity-[0.9]] mb-6 mx-auto"
        >
          <motion.h1
            className="Welcome-text text-[13px] text-center"
            initial={{ opacity: 0, filter: "blur(8px)" }}
            animate={isInView ? {
              opacity: 1,
              filter: "blur(0px)",
              transition: { duration: 0.8 }
            } : {}}
          >
            Who We Are
          </motion.h1>
        </motion.div>

        <motion.h2
          variants={{
            hidden: { opacity: 0, y: 20 },
            visible: {
              opacity: 1,
              y: 0,
              transition: {
                duration: 0.5,
                staggerChildren: 0.1
              }
            }
          }}
          className="text-4xl font-bold text-white mb-10 text-center"
        >
          <motion.div className="flex items-center justify-center">
            <motion.span
              variants={{
                hidden: { opacity: 0, y: 20 },
                visible: { opacity: 1, y: 0 }
              }}
            >
              About{" "}
            </motion.span>
            <motion.span
              variants={{
                hidden: { opacity: 0, y: 20 },
                visible: { opacity: 1, y: 0 }
              }}
              className="text-transparent bg-clip-text bg-gradient-to-r from-purple-500 to-cyan-500 inline-block"
            >
              Us
            </motion.span>
          </motion.div>
        </motion.h2>
      </motion.div>

      <div className="flex flex-col md:flex-row items-center justify-center gap-10 max-w-6xl mx-auto">
        {/* Left content */}
        <motion.div
          initial={{ opacity: 0, x: -50 }}
          animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -50 }}
          transition={{ duration: 0.7, delay: 0.2 }}
          className="flex-1 max-w-xl"
        >
          {/* Mission section with animated text */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <motion.h3
              className="text-2xl font-semibold text-white mb-4"
              initial={{ opacity: 0, x: -20 }}
              animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -20 }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              Our Mission
            </motion.h3>

            <AnimatedText
              text="At Oyu Intelligence LLC, we're a Mongolian Start-Up company dedicated to transforming businesses through innovative digital solutions. Our mission is to empower organizations with cutting-edge technology that drives growth, enhances efficiency, and creates exceptional user experiences."
              className="text-gray-300 mb-6"
              delay={0.5}
            />
          </motion.div>

          {/* Approach section with animated text */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.5, delay: 0.6 }}
          >
            <motion.h3
              className="text-2xl font-semibold text-white mb-4"
              initial={{ opacity: 0, x: -20 }}
              animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -20 }}
              transition={{ duration: 0.5, delay: 0.7 }}
            >
              Our Approach
            </motion.h3>

            <AnimatedText
              text="We believe in a collaborative approach, working closely with our clients to understand their unique challenges and goals. By combining technical expertise with creative thinking, we deliver tailored solutions that exceed expectations and provide lasting value."
              className="text-gray-300 mb-6"
              delay={0.8}
            />
          </motion.div>

          {/* Stats with animated counters */}
          <motion.div
            className="flex flex-wrap gap-4 mt-8"
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.5, delay: 1 }}
          >
            <AnimatedCounter value={100} label="Projects" color="purple" delay={1200} />
            <AnimatedCounter value={20} label="Clients" color="cyan" delay={1400} />
            <AnimatedCounter value={3} label="Brand Partners" color="purple" delay={1600} />
            <AnimatedCounter value={4} label="Services" color="cyan" delay={1800} />
          </motion.div>
        </motion.div>

        {/* Right image with parallax and floating effect */}
        <motion.div
          ref={imageRef}
          initial={{ opacity: 0, scale: 0.8, x: 50 }}
          animate={imageInView ? {
            opacity: 1,
            scale: 1,
            x: 0,
            transition: {
              duration: 0.8,
              delay: 0.3,
              type: "spring",
              stiffness: 100
            }
          } : { opacity: 0, scale: 0.8, x: 50 }}
          className="flex-1 flex justify-center"
          style={{ y, scale }}
        >
          <motion.div
            className="relative w-[300px] h-[300px] md:w-[400px] md:h-[400px]"
            animate={{
              y: [0, -10, 0, 10, 0],
              x: [0, 5, 0, -5, 0],
              rotate: [0, 1, 0, -1, 0]
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              repeatType: "mirror"
            }}
          >
            {/* Glow effect behind image */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-cyan-500/20 rounded-full blur-3xl"
              animate={{
                scale: [1, 1.1, 1, 0.95, 1],
                opacity: [0.5, 0.7, 0.5, 0.3, 0.5]
              }}
              transition={{ duration: 8, repeat: Infinity }}
            />

            <Image
              src="/hero-bg.svg"
              alt="About Oyu Intelligence"
              fill
              className="object-contain"
            />

            {/* Floating particles */}
            {[
              { id: "particle-1", color: "purple", position: { top: "20%", left: "10%" } },
              { id: "particle-2", color: "cyan", position: { top: "30%", left: "20%" } },
              { id: "particle-3", color: "purple", position: { top: "40%", left: "30%" } },
              { id: "particle-4", color: "cyan", position: { top: "50%", left: "40%" } },
              { id: "particle-5", color: "purple", position: { top: "60%", left: "50%" } },
              { id: "particle-6", color: "cyan", position: { top: "70%", left: "60%" } },
              { id: "particle-7", color: "purple", position: { top: "80%", left: "70%" } },
              { id: "particle-8", color: "cyan", position: { top: "90%", left: "80%" } },
            ].map((particle, index) => (
              <motion.div
                key={particle.id}
                className={`absolute w-2 h-2 rounded-full ${
                  particle.color === "purple" ? "bg-purple-500" : "bg-cyan-500"
                }`}
                style={{
                  top: particle.position.top,
                  left: particle.position.left,
                  opacity: 0.6,
                }}
                animate={{
                  y: [0, -15, 0, 15, 0],
                  x: [0, 10, 0, -10, 0],
                  opacity: [0.2, 0.6, 0.2, 0.6, 0.2],
                  scale: [0.8, 1.2, 0.8, 1.2, 0.8],
                }}
                transition={{
                  duration: 5 + index,
                  repeat: Infinity,
                  delay: index * 0.5,
                }}
              />
            ))}
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};
