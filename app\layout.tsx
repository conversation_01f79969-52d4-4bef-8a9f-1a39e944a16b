import type { Metadata, Viewport } from "next";
import { Inter } from "next/font/google";
import type { PropsWithChildren } from "react";

import { Footer } from "@/components/main/footer";
import { Navbar } from "@/components/main/navbar";
import { StarsCanvas } from "@/components/main/star-background";
import BlobCursor from "@/components/ui/blue-cursor";
import { siteConfig } from "@/config";
import { cn } from "@/lib/utils";

import "./globals.css";

// Add smooth scroll behavior
import "locomotive-scroll/dist/locomotive-scroll.css";

const inter = Inter({ subsets: ["latin"] });

export const viewport: Viewport = {
  themeColor: "#030014",
};

export const metadata: Metadata = siteConfig;

export default function RootLayout({ children }: Readonly<PropsWithChildren>) {
  return (
    <html lang="en">
      <body
        className={cn(
          "bg-[#030014] overflow-x-hidden",
          inter.className
        )}
      >
        <StarsCanvas />
        <BlobCursor
          blobType="circle"
          fillColor="#00f0ff"
          trailCount={3}
          sizes={[20, 35, 25]}
          innerSizes={[8, 12, 10]}
          innerColor="rgba(255,255,255,0.9)"
          opacities={[0.8, 0.6, 0.4]}
          shadowColor="rgba(59, 7, 100, 0.8)"
          shadowBlur={8}
          shadowOffsetX={2}
          shadowOffsetY={2}
          filterStdDeviation={15}
          useFilter={true}
          fastDuration={0.1}
          slowDuration={0.4}
          zIndex={100}
        />
        <Navbar />
        <div className="smooth-scroll-wrapper">
          {children}
        </div>
        <Footer />
      </body>
    </html>
  );
}
