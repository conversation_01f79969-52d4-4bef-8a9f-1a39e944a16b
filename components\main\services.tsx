"use client";

import {
  CheckCircleIcon,
  LightBulbIcon,
  SparklesIcon,
  StarIcon,
  RocketLaunchIcon
} from "@heroicons/react/24/outline";
import { motion, useInView } from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import React, { useRef, useState } from "react";

import {
  slideInFromLeft,
  slideInFromRight,
  slideInFromTop,
} from "@/lib/motion";

// Function to get a different icon for each feature
const getFeatureIcon = (index: number) => {
  const icons = [
    CheckCircleIcon,
    StarIcon,
    LightBulbIcon,
    SparklesIcon,
    RocketLaunchIcon
  ];
  return icons[index % icons.length];
};

// Service card component with hover effects and animations
const ServiceCard = ({
  title,
  description,
  iconSrc,
  index,
  features,
}: {
  title: string;
  description: string;
  iconSrc: string;
  index: number;
  features: string[];
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const cardRef = useRef(null);
  const isInView = useInView(cardRef, { once: true });

  return (
    <motion.div
      ref={cardRef}
      initial={{ opacity: 0, y: 50 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
      transition={{ duration: 0.5, delay: index * 0.2 }}
      className="relative group w-full sm:w-[calc(100%-1rem)] md:w-[calc(50%-2rem)] lg:w-[calc(50%-2rem)] xl:w-[calc(25%-2rem)] min-h-[400px] sm:min-h-[450px] mx-2 sm:mx-4 my-4 sm:my-6 perspective"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={() => setIsHovered(!isHovered)} // For mobile touch
    >
      <div
        className={`relative w-full h-full transition-all duration-500 preserve-3d ${
          isHovered ? "rotate-y-180" : ""
        }`}
      >
        {/* Front of card */}
        <div className="absolute w-full h-full backface-hidden">
          <div className="relative flex flex-col items-center justify-center p-6 sm:p-8 md:p-10 h-full overflow-hidden rounded-2xl backdrop-blur-xl bg-gradient-to-br from-[rgba(30,10,60,0.8)] via-[rgba(15,5,40,0.9)] to-[rgba(3,0,20,0.95)] border border-[#7042F88B] group-hover:border-purple-500/50 transition-all duration-500">

            {/* Enhanced animated background elements */}
            <div className="absolute top-0 right-0 w-32 sm:w-40 h-32 sm:h-40 bg-gradient-to-br from-purple-500/20 to-purple-600/10 rounded-full blur-3xl -mr-16 sm:-mr-20 -mt-16 sm:-mt-20 group-hover:from-purple-500/40 group-hover:to-purple-600/20 transition-all duration-700"></div>
            <div className="absolute bottom-0 left-0 w-32 sm:w-40 h-32 sm:h-40 bg-gradient-to-tr from-cyan-500/20 to-cyan-600/10 rounded-full blur-3xl -ml-16 sm:-ml-20 -mb-16 sm:-mb-20 group-hover:from-cyan-500/40 group-hover:to-cyan-600/20 transition-all duration-700"></div>

            {/* Floating particles */}
            <div className="absolute inset-0 pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-700">
              {[...Array(5)].map((_, i) => (
                <motion.div
                  key={`service-particle-${title}-${i}`}
                  className="absolute w-1 h-1 bg-gradient-to-r from-purple-400 to-cyan-400 rounded-full"
                  style={{
                    left: `${15 + i * 18}%`,
                    top: `${20 + i * 15}%`,
                  }}
                  animate={{
                    y: [-8, -18, -8],
                    opacity: [0.3, 0.9, 0.3],
                    scale: [0.5, 1.2, 0.5],
                  }}
                  transition={{
                    duration: 3 + i * 0.4,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: i * 0.3,
                  }}
                />
              ))}
            </div>

            {/* Enhanced service icon with multiple animation layers */}
            <motion.div
              className="w-20 h-20 sm:w-24 sm:h-24 md:w-28 md:h-28 relative mb-6 sm:mb-8"
              animate={{
                y: [0, -8, 0, 8, 0],
                scale: [1, 1.08, 1, 1.08, 1],
                rotate: [0, 2, 0, -2, 0],
              }}
              transition={{
                duration: 6,
                repeat: Infinity,
                repeatType: "mirror",
                ease: "easeInOut"
              }}
            >
              {/* Multiple glow layers */}
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500/40 to-cyan-500/40 rounded-full blur-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-700 scale-150"></div>
              <div className="absolute inset-0 bg-gradient-to-r from-purple-400/30 to-cyan-400/30 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 scale-125"></div>
              <div className="absolute inset-0 bg-gradient-to-r from-purple-300/20 to-cyan-300/20 rounded-full blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

              {/* Icon container with enhanced styling */}
              <div className="relative w-full h-full rounded-full bg-gradient-to-br from-purple-500/10 to-cyan-500/10 group-hover:from-purple-500/20 group-hover:to-cyan-500/20 transition-all duration-500 flex items-center justify-center">
                <Image
                  src={iconSrc}
                  alt={title}
                  fill
                  className="object-contain z-10 p-3 filter group-hover:brightness-110 transition-all duration-500"
                />
              </div>
            </motion.div>

            {/* Enhanced title with better gradient animation */}
            <motion.h3
              className="text-xl sm:text-2xl md:text-3xl font-bold text-white mb-3 sm:mb-4 text-center group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-purple-400 group-hover:to-cyan-400 transition-all duration-500"
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              {title}
            </motion.h3>

            {/* Enhanced description with better typography */}
            <p className="text-gray-300 group-hover:text-gray-200 text-center text-sm sm:text-base leading-relaxed mb-6 sm:mb-8 max-w-xs transition-colors duration-300">
              {description}
            </p>

            {/* Enhanced learn more button */}
            <div className="mt-auto">
              <motion.div
                className="relative group/button"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-cyan-600 rounded-full blur-lg opacity-0 group-hover:opacity-50 transition-opacity duration-300"></div>
                <motion.span
                  className="relative flex items-center text-sm sm:text-base text-purple-400 group-hover:text-purple-300 font-medium cursor-pointer px-4 py-2 rounded-full border border-purple-500/30 group-hover:border-purple-400/50 backdrop-blur-sm transition-all duration-300"
                  whileHover={{ x: 5 }}
                  transition={{ type: "spring", stiffness: 400 }}
                >
                  Learn More
                  <motion.svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 ml-2"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    animate={{ x: [0, 3, 0] }}
                    transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </motion.svg>
                </motion.span>
              </motion.div>
            </div>
          </div>
        </div>

        {/* Back of card */}
        <div className="absolute w-full h-full backface-hidden rotate-y-180">
          <div className="relative flex flex-col items-center justify-between p-6 sm:p-8 md:p-10 h-full overflow-hidden rounded-2xl backdrop-blur-xl bg-gradient-to-br from-[rgba(30,10,60,0.9)] via-[rgba(15,5,40,0.95)] to-[rgba(3,0,20,0.98)] border border-purple-500/50 shadow-2xl shadow-purple-900/30">

            {/* Enhanced background elements for back */}
            <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-purple-500/30 to-purple-600/20 rounded-full blur-3xl -mr-16 -mt-16"></div>
            <div className="absolute bottom-0 left-0 w-32 h-32 bg-gradient-to-tr from-cyan-500/30 to-cyan-600/20 rounded-full blur-3xl -ml-16 -mb-16"></div>

            {/* Enhanced title with better gradient */}
            <motion.h3
              className="text-xl sm:text-2xl md:text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 via-purple-300 to-cyan-400 mb-4 sm:mb-6 text-center"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              {title}
            </motion.h3>

            {/* Enhanced features list with better animations */}
            <ul className="space-y-3 sm:space-y-4 w-full flex-grow">
              {features.map((feature, i) => (
                <motion.li
                  key={`feature-${title}-${i}`}
                  className="flex items-start group/feature"
                  initial={{ opacity: 0, x: -20, scale: 0.9 }}
                  animate={{ opacity: 1, x: 0, scale: 1 }}
                  transition={{
                    duration: 0.4,
                    delay: 0.3 + i * 0.1,
                    type: "spring",
                    stiffness: 200
                  }}
                  whileHover={{ x: 5, scale: 1.02 }}
                >
                  <motion.div
                    className="relative mr-3 mt-0.5"
                    whileHover={{ rotate: 360, scale: 1.2 }}
                    transition={{ duration: 0.5 }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-purple-500/30 to-cyan-500/30 rounded-full blur-sm opacity-0 group-hover/feature:opacity-100 transition-opacity duration-300"></div>
                    {React.createElement(getFeatureIcon(i), {
                      className: "relative h-5 w-5 sm:h-6 sm:w-6 text-purple-400 group-hover/feature:text-purple-300 transition-colors duration-300"
                    })}
                  </motion.div>
                  <span className="text-gray-300 group-hover/feature:text-gray-200 text-sm sm:text-base font-medium transition-colors duration-300 leading-relaxed">
                    {feature}
                  </span>
                </motion.li>
              ))}
            </ul>

            {/* Enhanced CTA button */}
            <motion.div
              className="mt-6 sm:mt-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.8 }}
            >
              <motion.div
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                className="relative group/cta"
              >
                {/* Enhanced button with multiple layers */}
                <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-cyan-600 rounded-full blur-lg opacity-50 group-hover/cta:opacity-75 transition-opacity duration-300"></div>
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-cyan-500 rounded-full opacity-0 group-hover/cta:opacity-20 transition-opacity duration-300"></div>

                <Link
                  href={`/projects?category=${encodeURIComponent(title)}`}
                  className="relative block px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-purple-600 to-cyan-600 text-white rounded-full text-sm sm:text-base font-semibold hover:from-purple-500 hover:to-cyan-500 transition-all duration-300 shadow-lg shadow-purple-900/30 backdrop-blur-sm border border-purple-400/30"
                >
                  <span className="flex items-center justify-center">
                    View Projects
                    <motion.svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4 ml-2"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                      animate={{ x: [0, 3, 0] }}
                      transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                    </motion.svg>
                  </span>
                </Link>
              </motion.div>
            </motion.div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export const Services = () => {
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, { once: true, amount: 0.2 });

  const services = [
    {
      title: "AI Automation",
      description: "Leverage cutting-edge artificial intelligence to automate repetitive tasks, analyze data, and make intelligent decisions for your business.",
      iconSrc: "/skills/artificial-intelligence.png",
      features: [
        "Natural Language Processing",
        "Machine Learning Solutions",
        "Predictive Analytics",
        "Process Automation",
        "Custom AI Development"
      ]
    },
    {
      title: "Mobile App Development",
      description: "Create stunning, high-performance mobile applications for iOS and Android that engage users and drive business growth.",
      iconSrc: "/skills/application.png",
      features: [
        "iOS & Android Development",
        "Cross-Platform Solutions",
        "UI/UX Design",
        "App Performance Optimization",
        "Maintenance & Support"
      ]
    },
    {
      title: "Website Design & Development",
      description: "Build responsive, modern websites with beautiful designs and seamless functionality that represent your brand perfectly.",
      iconSrc: "/skills/dns.png",
      features: [
        "Responsive Web Design",
        "E-commerce Solutions",
        "CMS Development",
        "Web Application Development",
        "Performance Optimization"
      ]
    },
    {
      title: "Social Media Development",
      description: "Enhance your social media presence with custom solutions, integrations, and strategies that connect with your audience.",
      iconSrc: "/skills/social-media.png",
      features: [
        "Social Media Strategy",
        "Content Creation",
        "Platform Integration",
        "Analytics & Reporting",
        "Campaign Management"
      ]
    },
  ];

  return (
    <section
      id="services-detail"
      ref={sectionRef}
      className="relative flex flex-col items-center justify-center py-12 sm:py-16 md:py-20 px-4 overflow-hidden"
    >
      {/* Background elements */}
      <div className="absolute top-1/4 left-0 w-48 sm:w-60 md:w-72 h-48 sm:h-60 md:h-72 bg-purple-900/20 rounded-full blur-3xl"></div>
      <div className="absolute bottom-1/4 right-0 w-48 sm:w-60 md:w-72 h-48 sm:h-60 md:h-72 bg-cyan-900/20 rounded-full blur-3xl"></div>

      <motion.div
        variants={slideInFromTop}
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
        className="Welcome-box py-[8px] px-[7px] border border-[#7042f88b] opacity-[0.9]] mb-4 sm:mb-6"
      >
        <h1 className="Welcome-text text-[13px]">
          What We Offer
        </h1>
      </motion.div>

      <motion.h2
        variants={slideInFromLeft(0.5)}
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
        className="text-3xl sm:text-4xl font-bold text-white mb-3 sm:mb-4 text-center px-2"
      >
        Our <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-500 to-cyan-500">Services</span>
      </motion.h2>

      <motion.p
        variants={slideInFromRight(0.5)}
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
        className="text-gray-400 text-center max-w-2xl mb-10 sm:mb-16 px-4 text-sm sm:text-base"
      >
        We provide comprehensive digital solutions tailored to your business needs.
        Our expertise spans across multiple domains to deliver exceptional results.
      </motion.p>

      <div className="w-full max-w-7xl mx-auto">
        <div className="flex flex-wrap justify-center -mx-2 sm:-mx-4">
          {services.map((service, index) => (
            <ServiceCard
              key={service.title}
              title={service.title}
              description={service.description}
              iconSrc={service.iconSrc}
              features={service.features}
              index={index}
            />
          ))}
        </div>
      </div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
        transition={{ duration: 0.5, delay: 0.8 }}
        className="mt-10 sm:mt-16 text-center"
      >
        <Link
          href="/projects"
          className="px-6 sm:px-8 py-2 sm:py-3 button-primary text-white rounded-lg hover:opacity-90 transition-opacity text-sm sm:text-base"
        >
          View All Projects
        </Link>
      </motion.div>
    </section>
  );
};
