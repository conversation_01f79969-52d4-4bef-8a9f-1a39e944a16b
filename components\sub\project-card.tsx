import { motion } from "framer-motion";
import Image from "next/image";
import { GlowingEffect } from "@/components/ui/glowing-effect";

type ProjectCardProps = {
  src: string;
  title: string;
  description: string;
  link?: string; // Optional since we're not using it directly
  isLink?: boolean;
  category?: string;
};

export const ProjectCard = ({
  src,
  title,
  description,
  link: _link, // Renamed to avoid unused variable warning
  isLink = false,
  category,
}: ProjectCardProps) => {
  // Animation variants
  const imageVariants = {
    hover: {
      scale: 1.1,
      transition: { duration: 0.5, ease: [0.25, 0.1, 0.25, 1] }
    }
  };

  const arrowVariants = {
    hover: {
      x: 5,
      transition: {
        repeat: Infinity,
        repeatType: "mirror" as const,
        duration: 0.8,
        ease: "easeInOut"
      }
    }
  };

  const CardContent = () => (
    <motion.div
      className="h-full flex flex-col relative group"
      initial="initial"
      whileHover="hover"
    >
      {/* Enhanced image container with overlay effects */}
      <div className="relative h-[240px] overflow-hidden rounded-t-2xl">
        {/* Background gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-transparent to-cyan-900/20 z-10" />

        {/* Animated image */}
        <motion.div
          variants={imageVariants}
          className="h-full w-full relative"
        >
          <Image
            src={src}
            alt={title}
            fill
            className="object-cover transition-all duration-700 group-hover:brightness-110"
          />
          {/* Dynamic overlay that responds to hover */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-60 group-hover:opacity-30 transition-opacity duration-500 z-20" />
        </motion.div>

        {/* Enhanced category badge */}
        {category && (
          <motion.div
            className="absolute top-4 right-4 z-30"
            whileHover={{
              scale: 1.1,
              rotate: 5
            }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-cyan-600 rounded-full blur-sm opacity-75" />
              <div className="relative bg-gradient-to-r from-purple-600 to-cyan-600 text-white text-xs font-semibold px-3 py-1.5 rounded-full shadow-lg backdrop-blur-sm">
                {category}
              </div>
            </div>
          </motion.div>
        )}

        {/* Floating particles on hover */}
        <div className="absolute inset-0 pointer-events-none z-20 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
          {[...Array(4)].map((_, i) => (
            <motion.div
              key={`project-particle-${title}-${i}`}
              className="absolute w-1 h-1 bg-gradient-to-r from-purple-400 to-cyan-400 rounded-full"
              style={{
                left: `${20 + i * 20}%`,
                top: `${30 + i * 15}%`,
              }}
              animate={{
                y: [-5, -15, -5],
                opacity: [0.3, 0.8, 0.3],
                scale: [0.5, 1, 0.5],
              }}
              transition={{
                duration: 2 + i * 0.5,
                repeat: Infinity,
                ease: "easeInOut",
                delay: i * 0.2,
              }}
            />
          ))}
        </div>
      </div>

      {/* Enhanced content section */}
      <div className="relative p-6 flex flex-col flex-grow bg-gradient-to-br from-[rgba(15,5,30,0.95)] to-[rgba(3,0,20,0.98)] rounded-b-2xl">
        {/* Subtle background pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-purple-500/10 to-cyan-500/10 rounded-b-2xl" />
        </div>

        <div className="relative z-10">
          {/* Enhanced title with gradient on hover */}
          <motion.h1
            className="text-2xl font-bold text-white mb-3 group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-purple-400 group-hover:to-cyan-400 transition-all duration-500"
            whileHover={{ scale: 1.02 }}
          >
            {title}
          </motion.h1>

          {/* Enhanced description */}
          <p className="text-gray-300 flex-grow leading-relaxed text-sm mb-6 group-hover:text-gray-200 transition-colors duration-300">
            {description}
          </p>

          {/* Enhanced CTA with better animation */}
          <div className="mt-auto flex justify-between items-center">
            <motion.div
              className="flex items-center space-x-2 text-purple-400 group-hover:text-purple-300 transition-colors duration-300"
              whileHover={{ x: 5 }}
              transition={{ type: "spring", stiffness: 400 }}
            >
              <span className="text-sm font-medium">
                {isLink ? "View Details" : "Visit Project"}
              </span>
              <motion.svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                variants={arrowVariants}
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </motion.svg>
            </motion.div>

            {/* Tech stack indicator dots */}
            <div className="flex space-x-1">
              {[...Array(3)].map((_, i) => (
                <motion.div
                  key={`tech-${title}-${i}`}
                  className="w-2 h-2 rounded-full bg-gradient-to-r from-purple-500 to-cyan-500 opacity-60 group-hover:opacity-100"
                  animate={{
                    scale: [1, 1.2, 1],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    delay: i * 0.3,
                  }}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );

  return (
    <motion.div
      className="relative overflow-hidden rounded-2xl h-full will-change-transform group"
      whileHover={{
        y: -8,
        transition: { duration: 0.3, ease: "easeOut" }
      }}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Enhanced glassmorphism background */}
      <div className="absolute inset-0 bg-gradient-to-br from-[rgba(30,10,60,0.4)] via-[rgba(15,5,40,0.6)] to-[rgba(3,0,20,0.8)] backdrop-blur-xl rounded-2xl" />

      {/* Animated border gradient */}
      <div className="absolute inset-0 rounded-2xl p-[1px] bg-gradient-to-r from-purple-500/50 via-cyan-500/50 to-purple-500/50 bg-[length:200%_200%] animate-gradient-xy opacity-0 group-hover:opacity-100 transition-opacity duration-500">
        <div className="h-full w-full rounded-2xl bg-gradient-to-br from-[rgba(30,10,60,0.95)] via-[rgba(15,5,40,0.98)] to-[rgba(3,0,20,1)]" />
      </div>

      {/* Enhanced glowing effect */}
      <GlowingEffect
        spread={120}
        glow={true}
        disabled={false}
        proximity={180}
        inactiveZone={0.03}
        blur={6}
        borderWidth={2}
      />

      {/* Content */}
      <div className="relative z-10">
        <CardContent />
      </div>

      {/* Subtle shadow enhancement */}
      <div className="absolute inset-0 rounded-2xl shadow-2xl shadow-purple-900/20 group-hover:shadow-purple-500/30 transition-shadow duration-500 pointer-events-none" />
    </motion.div>
  );
};
