import { BlogDetailPage } from "@/components/pages/blog-detail-page";
import { BLOG_POSTS } from "@/constants/blog";
import { notFound } from "next/navigation";

export async function generateStaticParams() {
  return BLOG_POSTS.map((post) => ({
    slug: post.slug,
  }));
}

export default function BlogDetail({ params }: { params: { slug: string } }) {
  const post = BLOG_POSTS.find((p) => p.slug === params.slug);
  
  if (!post) {
    notFound();
  }
  
  return <BlogDetailPage post={post} />;
}
